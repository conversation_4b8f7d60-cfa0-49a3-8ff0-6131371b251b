#include "jit_cache.h"
#include <algorithm>
#include <spdlog/spdlog.h>

#ifdef _WIN32
#include <windows.h>
#else
#include <sys/mman.h>
#include <unistd.h>
#endif

namespace jit {

void JitBlock::freeExecutableMemory() {
    if (!code || size == 0) return;

    // CRITICAL: Bounds check for memory operations
    if (size > 1024 * 1024 * 1024) { // 1GB sanity check
        spdlog::error("JitBlock: Invalid size {} for memory free", size);
        return;
    }

#ifdef _WIN32
    VirtualFree(code, 0, MEM_RELEASE);
#else
    munmap(code, size);
#endif
    code = nullptr;
    size = 0;
}

JitCache::JitCache() : maxSize_(64 * 1024 * 1024), currentSize_(0) {
    stats_ = {};
}

JitCache::~JitCache() {
    clear();
}

JitBlock& JitCache::getOrCreate(uint64_t pc) {
    std::lock_guard<std::mutex> lock(mutex_);

    auto it = blocks_.find(pc);
    if (it != blocks_.end()) {
        updateStats(true);
        it->second->lastUsed = stats_.hits + stats_.misses;
        return *it->second;
    }

    updateStats(false);

    // Check if we need to evict blocks
    // CRITICAL: Bounds check to prevent infinite loop
    size_t evictionCount = 0;
    while (currentSize_ >= maxSize_ && !blocks_.empty() && evictionCount < blocks_.size()) {
        evictLRU();
        evictionCount++;
    }

    // Create new block
    auto block = std::make_unique<JitBlock>(pc);
    JitBlock& blockRef = *block;
    blocks_[pc] = std::move(block);
    stats_.blockCount++;

    spdlog::trace("JitCache: Created new block for PC 0x{:x}", pc);
    return blockRef;
}

JitBlock* JitCache::find(uint64_t pc) {
    std::lock_guard<std::mutex> lock(mutex_);

    auto it = blocks_.find(pc);
    if (it != blocks_.end()) {
        updateStats(true);
        it->second->lastUsed = stats_.hits + stats_.misses;
        return it->second.get();
    }

    updateStats(false);
    return nullptr;
}

void JitCache::remove(uint64_t pc) {
    std::lock_guard<std::mutex> lock(mutex_);

    auto it = blocks_.find(pc);
    if (it != blocks_.end()) {
        currentSize_ -= it->second->size;
        blocks_.erase(it);
        stats_.blockCount--;
        spdlog::trace("JitCache: Removed block for PC 0x{:x}", pc);
    }
}

void JitCache::clear() {
    std::lock_guard<std::mutex> lock(mutex_);

    blocks_.clear();
    currentSize_ = 0;
    stats_.blockCount = 0;
    spdlog::info("JitCache: Cleared all blocks");
}

JitCache::Stats JitCache::getStats() const {
    std::lock_guard<std::mutex> lock(mutex_);
    Stats result = stats_;
    result.totalSize = currentSize_;
    return result;
}

void JitCache::setMaxSize(size_t maxSize) {
    std::lock_guard<std::mutex> lock(mutex_);
    maxSize_ = maxSize;

    // Evict blocks if current size exceeds new max
    while (currentSize_ > maxSize_ && !blocks_.empty()) {
        evictLRU();
    }
}

size_t JitCache::getMaxSize() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return maxSize_;
}

void JitCache::evictLRU() {
    if (blocks_.empty()) return;

    auto lruIt = std::min_element(blocks_.begin(), blocks_.end(),
        [](const auto& a, const auto& b) {
            return a.second->lastUsed < b.second->lastUsed;
        });

    if (lruIt != blocks_.end()) {
        currentSize_ -= lruIt->second->size;
        spdlog::trace("JitCache: Evicted LRU block for PC 0x{:x}", lruIt->first);
        blocks_.erase(lruIt);
        stats_.evictions++;
        stats_.blockCount--;
    }
}

void JitCache::updateStats(bool hit) {
    if (hit) {
        stats_.hits++;
    } else {
        stats_.misses++;
    }
}

} // namespace jit
