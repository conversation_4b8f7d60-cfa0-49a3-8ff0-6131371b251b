// Copyright 2025 <Copyright Owner>

#include "tlb.h"
#include "../common/lock_ordering.h"
#include "../debug/vector_debug.h"
#include <algorithm>
#include <chrono>
#include <spdlog/spdlog.h>

namespace ps4 {

/**
 * @brief Constructs the TLB with default settings.
 */
TLB::TLB() {
  sets.resize(numSets, std::array<Entry, DEFAULT_WAYS>()); // Initialize sets
  lruIndices.resize(numSets); // Initialize LRU indices per set
  spdlog::info("TLB initialized with max entries: {}", MAX_ENTRIES);
}

/**
 * @brief Adds a TLB entry.
 * @param virtualAddr Virtual address (page-aligned).
 * @param physicalAddr Physical address.
 * @param flags Protection flags.
 * @param pageSize Page size (e.g., 4KB, 2MB, 1GB).
 * @param processId Process ID for tagged entries.
 * @throws TLBException on invalid page size or address.
 */
void TLB::AddEntry(uint64_t virtualAddr, uint64_t physicalAddr, uint32_t flags,
                   size_t pageSize, uint64_t processId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    size_t setIndex = (virtualAddr / PAGE_SIZE) % numSets;

    // CRITICAL: Bounds check for TLB set access
    if (setIndex >= numSets) {
      spdlog::error("TLB::AddEntry: setIndex {} >= numSets {}", setIndex,
                    numSets);
      throw TLBException("TLB set index out of bounds");
    }

    auto &set =
        CRITICAL_VECTOR_ACCESS(sets, setIndex, "TLB::AddEntry sets access");
    auto &lru = CRITICAL_VECTOR_ACCESS(lruIndices, setIndex,
                                       "TLB::AddEntry lruIndices access");

    // Check for existing entry and update if found
    for (size_t i = 0; i < ways && i < set.size(); ++i) {
      auto &entry =
          CRITICAL_VECTOR_ACCESS(set, i, "TLB::AddEntry entry access");
      if (entry.virtualAddr == (virtualAddr & ~GetPageMask(pageSize)) &&
          entry.processId == processId) {
        entry.physicalAddr = physicalAddr;
        entry.flags = flags;
        entry.pageSize = pageSize;
        // Move to front of LRU
        lru.remove(i);
        lru.push_front(i);
        m_stats.hits++;
        auto end = std::chrono::steady_clock::now();
        m_stats.totalLatencyUs +=
            std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                .count();
#ifdef DEBUG_TLB
        spdlog::trace("TLB: Updated entry in set {}: VA=0x{:x}, PA=0x{:x}",
                      setIndex, virtualAddr, physicalAddr);
#endif
        return;
      }
    }

    // Find empty way or evict LRU
    size_t index = SIZE_MAX;
    for (size_t i = 0; i < ways; ++i) {
      if (set[i].virtualAddr ==
          0) { // Empty slot (virtualAddr initialized to 0)
        index = i;
        break;
      }
    }
    if (index == SIZE_MAX) {
      index = lru.back();
      lru.pop_back();
      m_stats.evictions++;
#ifdef DEBUG_TLB
      spdlog::trace("TLB: Evicted entry in set {} at way {}", setIndex, index);
#endif
    }
    // Insert new entry
    set[index].virtualAddr = virtualAddr & ~GetPageMask(pageSize);
    set[index].physicalAddr = physicalAddr;
    set[index].flags = flags;
    set[index].pageSize = pageSize;
    set[index].processId = processId;
    lru.push_front(index);
    m_stats.inserts++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
#ifdef DEBUG_TLB
    spdlog::trace("TLB: Added entry in set {} at way {}: VA=0x{:x}, PA=0x{:x}",
                  setIndex, index, virtualAddr, physicalAddr);
#endif
  } catch (const TLBException &e) {
#ifdef DEBUG_TLB
    spdlog::error("TLB AddEntry failed: {}", e.what());
#endif
    throw;
  } catch (const std::exception &e) {
#ifdef DEBUG_TLB
    spdlog::error("Unexpected error in TLB AddEntry: {}", e.what());
#endif
    throw TLBException("AddEntry failure");
  }
}

/**
 * @brief Looks up a virtual address.
 * @param virtualAddr Virtual address.
 * @param physicalAddr Output physical address.
 * @param pageSize Output page size.
 * @param processId Process ID for tagged lookup.
 * @return True if found, false otherwise.
 */
bool TLB::Lookup(uint64_t virtualAddr, uint64_t &physicalAddr, size_t &pageSize,
                 uint64_t processId) const {
  auto start = std::chrono::steady_clock::now();
  MEMORY_SHARED_LOCK(m_mutex, "TLBMutex");
  size_t setIndex = (virtualAddr / PAGE_SIZE) % numSets;

  // CRITICAL: Bounds check for TLB set access
  if (setIndex >= numSets) {
    spdlog::error("TLB::Lookup: setIndex {} >= numSets {}", setIndex, numSets);
    const_cast<Stats &>(m_stats).misses++;
    auto end = std::chrono::steady_clock::now();
    const_cast<Stats &>(m_stats).totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return false;
  }

  // Validate ways doesn't exceed array bounds
  if (ways > DEFAULT_WAYS) {
    spdlog::error("TLB::Lookup: ways {} > DEFAULT_WAYS {}", ways, DEFAULT_WAYS);
    const_cast<Stats &>(m_stats).misses++;
    auto end = std::chrono::steady_clock::now();
    const_cast<Stats &>(m_stats).totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return false;
  }

  const auto &set = CRITICAL_VECTOR_ACCESS(sets, setIndex, "TLB::Lookup sets access");
  auto &lru = const_cast<std::list<size_t> &>(
      CRITICAL_VECTOR_ACCESS(lruIndices, setIndex, "TLB::Lookup lruIndices access")); // Allow LRU updates

  for (size_t i = 0; i < ways && i < set.size(); ++i) {
    const auto &entry = SAFE_STD_ARRAY_ACCESS(set, i, "TLB::Lookup entry access");
    if (entry.virtualAddr == (virtualAddr & ~GetPageMask(entry.pageSize)) &&
        entry.processId == processId) {
      physicalAddr = entry.physicalAddr;
      pageSize = entry.pageSize;
      // Update LRU: move accessed way to front
      lru.remove(i);
      lru.push_front(i);
      const_cast<Stats &>(m_stats).hits++;
      auto end = std::chrono::steady_clock::now();
      const_cast<Stats &>(m_stats).totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
#ifdef DEBUG_TLB
      spdlog::trace("TLB: Hit in set {}: VA=0x{:x}, PA=0x{:x}", setIndex,
                    virtualAddr, physicalAddr);
#endif
      return true;
    }
  }
  const_cast<Stats &>(m_stats).misses++;
  auto end = std::chrono::steady_clock::now();
  const_cast<Stats &>(m_stats).totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
#ifdef DEBUG_TLB
  spdlog::trace("TLB: Miss for VA=0x{:x}, pid={}", virtualAddr, processId);
#endif
  return false;
}

/**
 * @brief Invalidates a TLB entry.
 * @param virtualAddr Virtual address.
 * @param processId Process ID for tagged invalidation.
 */
void TLB::Invalidate(uint64_t virtualAddr, uint64_t processId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  for (auto it = m_entries.begin(); it != m_entries.end(); ++it) {
    uint64_t pageMask = GetPageMask(it->second.pageSize);
    if ((virtualAddr & ~pageMask) == it->first &&
        it->second.processId == processId) {
      spdlog::trace("TLB: Invalidated VA=0x{:x}, pid={}", it->first, processId);
      m_entries.erase(it);
      m_lruList.remove(it->first);
      m_stats.evictions++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      return;
    }
  }
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Clears all TLB entries.
 */
void TLB::Clear() {
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    m_entries.clear();
    m_lruList.clear();
    m_stats = Stats();
    spdlog::trace("TLB: Cleared all entries");
  } catch (const std::exception &e) {
    spdlog::error("TLB Clear failed: {}", e.what());
    throw TLBException("Clear failure");
  }
}

/**
 * @brief Inserts a TLB entry (simplified interface).
 * @param virtualAddr Virtual address.
 * @param physAddr Physical address.
 * @param pageSize Page size.
 * @param processId Process ID for tagged entries.
 */
void TLB::Insert(uint64_t virtualAddr, uint64_t physAddr, size_t pageSize,
                 uint64_t processId) {
  AddEntry(virtualAddr, physAddr, 0, pageSize, processId);
}

/**
 * @brief Gets the page mask for a given page size.
 * @param pageSize Page size.
 * @return Page mask.
 * @throws TLBException on invalid page size.
 */
uint64_t TLB::GetPageMask(size_t pageSize) const {
  switch (pageSize) {
  case 4096:
    return 0xFFF;
  case 2 * 1024 * 1024:
    return 0x1FFFFF;
  case 1 * 1024 * 1024 * 1024:
    return 0x3FFFFFFF;
  default:
    spdlog::error("Invalid TLB page size: {}", pageSize);
    throw TLBException("Invalid page size");
  }
}

/**
 * @brief Evicts an entry if the TLB is full.
 */
void TLB::EvictIfNeeded() {
  try {
    if (m_entries.size() >= MAX_ENTRIES) {
      uint64_t lruAddr = m_lruList.back();
      m_lruList.pop_back();
      m_entries.erase(lruAddr);
      m_stats.evictions++;
      spdlog::trace("TLB: Evicted VA=0x{:x}", lruAddr);
    }
  } catch (const std::exception &e) {
    spdlog::error("TLB EvictIfNeeded failed: {}", e.what());
    throw TLBException("Eviction failure");
  }
}

/**
 * @brief Saves the TLB state to a stream.
 * @param out Output stream.
 */
void TLB::SaveState(std::ostream &out) const {
  MEMORY_SHARED_LOCK(m_mutex, "TLBMutex");
  try {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    uint64_t entryCount = m_entries.size();
    out.write(reinterpret_cast<const char *>(&entryCount), sizeof(entryCount));
    for (const auto &[addr, entry] : m_entries) {
      out.write(reinterpret_cast<const char *>(&addr), sizeof(addr));
      out.write(reinterpret_cast<const char *>(&entry), sizeof(entry));
    }
    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));
    if (!out.good()) {
      throw TLBException("Failed to write TLB state");
    }
    spdlog::info("TLB state saved: {} entries", entryCount);
  } catch (const std::exception &e) {
    spdlog::error("TLB SaveState failed: {}", e.what());
    throw TLBException("Save state failure");
  }
}

/**
 * @brief Loads the TLB state from a stream.
 * @param in Input stream.
 */
void TLB::LoadState(std::istream &in) {
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      spdlog::error("Unsupported TLB state version: {}", version);
      throw TLBException("Invalid TLB state version");
    }
    m_entries.clear();
    m_lruList.clear();
    uint64_t entryCount;
    in.read(reinterpret_cast<char *>(&entryCount), sizeof(entryCount));
    for (uint64_t i = 0; i < entryCount && in.good(); ++i) {
      uint64_t addr;
      Entry entry;
      in.read(reinterpret_cast<char *>(&addr), sizeof(addr));
      in.read(reinterpret_cast<char *>(&entry), sizeof(entry));
      m_entries[addr] = entry;
      m_lruList.push_front(addr);
    }
    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));
    if (!in.good()) {
      throw TLBException("Failed to read TLB state");
    }
    spdlog::info("TLB state loaded: {} entries", entryCount);
  } catch (const std::exception &e) {
    spdlog::error("TLB LoadState failed: {}", e.what());
    throw TLBException("Load state failure");
  }
}

// Cleanup stale TLB entries to maintain efficiency
void TLB::CleanupStaleEntries() {
#ifdef DEBUG_TLB
  spdlog::info("Cleaning up stale TLB entries");
#endif
  // Implement logic to remove entries that haven't been accessed recently
  // This could be based on a timestamp or a simple LRU policy extension
  if (m_entries.size() > MAX_ENTRIES * 0.8) { // Threshold for cleanup
    while (m_entries.size() > MAX_ENTRIES * 0.6 && !m_lruList.empty()) {
      uint64_t lruAddr = m_lruList.back();
      m_lruList.pop_back();
      m_entries.erase(lruAddr);
      m_stats.evictions++;
#ifdef DEBUG_TLB
      spdlog::trace("TLB: Cleaned stale entry VA=0x{:x}", lruAddr);
#endif
    }
  }
}

} // namespace ps4