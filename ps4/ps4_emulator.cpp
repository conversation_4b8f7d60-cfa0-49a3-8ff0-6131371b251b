#include "ps4_emulator.h"
#include "../common/lock_ordering.h"
#include "../cpu/cpu_diagnostics.h"
#include "../cpu/register.h"
#include "../cpu/x86_64_cpu.h"
#include "../emulator/interrupt_handler.h"
#include "../emulator/io_manager.h"
#include "../jit/jit_diagnostics.h"
#include "../jit/x86_64_jit_compiler.h"
#include "../loader/pkg_installer.h"
#include "../memory/memory.h"
#include "../memory/memory_diagnostics.h"
#include "../memory/ps4_mmu.h"
#include "../memory/tlb.h"
#include "../syscall/syscall_handler.h"
#include "../video_core/command_processor.h"
#include "../video_core/gnm_shader_translator.h"
#include "../video_core/tile_manager.h"
#include "fiber_manager.h"
#include "orbis_os.h"
#include "ps4_audio.h"
#include "ps4_controllers.h"
#include "ps4_filesystem.h"
#include "ps4_gpu.h"
#include "ps4_tsc.h"
#include "trophy_manager.h"
#include "zlib_wrapper.h"
#include <SDL2/SDL.h>
#include <algorithm>
#include <chrono>
#include <filesystem>
#include <fstream>
#include <functional>
#include <spdlog/fmt/fmt.h>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <thread>
#include <unordered_map>

// Platform-specific includes for system info
#ifdef _WIN32
#include <windows.h>
#else
#include <sys/statvfs.h>
#include <sys/sysinfo.h>

#endif

namespace ps4 {
// Define singleton storage
PS4Emulator *PS4Emulator::s_instance = nullptr;

// DeadlockDetector and EnhancedStats are now defined in ps4_emulator.h

/**
 * @brief Constructs the emulator.
 */
PS4Emulator::PS4Emulator() : m_running(false), m_paused(false) {
  auto start = std::chrono::steady_clock::now();
  s_instance = this;
  m_stats = Stats();
  m_enhancedStats = std::make_unique<ps4::EnhancedStats>();
  m_deadlockDetector = std::make_unique<ps4::DeadlockDetector>();
  spdlog::info("PS4Emulator constructed");
  auto end = std::chrono::steady_clock::now();
  auto latency =
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
}

/**
 * @brief Destructor, cleaning up resources.
 */
PS4Emulator::~PS4Emulator() {
  auto start = std::chrono::steady_clock::now();
  Shutdown();
  s_instance = nullptr;
  spdlog::info("PS4Emulator destroyed");
  auto end = std::chrono::steady_clock::now();
  auto latency =
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
}

/**
 * @brief Singleton accessor.
 * @return Reference to the emulator instance.
 */
PS4Emulator &PS4Emulator::GetInstance() {
  if (!s_instance) {
    throw PS4EmulatorException("No emulator instance exists");
  }
  return *s_instance;
}

// --- ADD: System requirements validation ---
bool PS4Emulator::ValidateSystemRequirements() {
  // Helper to get system memory
  auto GetSystemMemory = []() -> uint64_t {
#ifdef _WIN32
    MEMORYSTATUSEX statex;
    statex.dwLength = sizeof(statex);
    GlobalMemoryStatusEx(&statex);
    return statex.ullTotalPhys;
#else
    struct sysinfo info;
    if (sysinfo(&info) == 0) {
      return info.totalram * info.mem_unit;
    }
    return 0;
#endif
  };

  // Helper to check CPU features (simplified)
  auto CheckCPUFeatures = []() -> bool {
    // In a real implementation, this would use CPUID instructions
    // For this example, we'll assume modern CPUs have these features.
    return true;
  };

  // Helper to get free disk space
  auto GetFreeDiskSpace = []() -> uint64_t {
#ifdef _WIN32
    ULARGE_INTEGER freeBytesAvailable;
    if (GetDiskFreeSpaceExA(".", &freeBytesAvailable, nullptr, nullptr)) {
      return freeBytesAvailable.QuadPart;
    }
    return 0;
#else
    struct statvfs stat;
    if (statvfs(".", &stat) == 0) {
      return stat.f_bavail * stat.f_frsize;
    }
    return 0;
#endif
  };

  // Check available memory
  auto totalMemory = GetSystemMemory();
  if (totalMemory < 8ULL * 1024 * 1024 * 1024) { // 8GB minimum
    spdlog::error(
        "Insufficient system memory: {} bytes available, 8GB required",
        totalMemory);
    return false;
  }

  // Check CPU features
  if (!CheckCPUFeatures()) {
    spdlog::error(
        "Required CPU features not available (SSE4.2, AVX2 required)");
    return false;
  }

  // Check available disk space for save states
  auto freeSpace = GetFreeDiskSpace();
  if (freeSpace < 10ULL * 1024 * 1024 * 1024) { // 10GB minimum
    spdlog::warn("Low disk space: {} bytes available", freeSpace);
  }

  return true;
}

// --- ADD: New helper method for initialization with rollback ---
bool PS4Emulator::InitializeComponentWithRollback(
    std::function<bool()> initFunc, std::function<void()> cleanupFunc,
    std::vector<std::function<void()>> &cleanupFunctions,
    const std::string &componentName) {

  try {
    if (!initFunc()) {
      spdlog::error("{} initialization failed", componentName);
      return false;
    }
    cleanupFunctions.push_back(cleanupFunc);
    spdlog::info("{} initialized successfully", componentName);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("{} initialization threw exception: {}", componentName,
                  e.what());
    return false;
  }
}

bool PS4Emulator::InitializeComponentWithTimeout(
    std::function<bool()> initFunc, std::function<void()> cleanupFunc,
    std::vector<std::function<void()>> &cleanupFunctions,
    const std::string &componentName, int timeoutSeconds) {

  spdlog::info("Initializing {} with {}s timeout...", componentName,
               timeoutSeconds);
  auto start = std::chrono::steady_clock::now();

  try {
    std::atomic<bool> initComplete{false};
    std::atomic<bool> initResult{false};
    std::exception_ptr initException = nullptr;

    // Run initialization in a separate thread to enable timeout
    std::thread initThread([&]() {
      try {
        bool result = initFunc();
        initResult.store(result);
      } catch (...) {
        initException = std::current_exception();
      }
      initComplete.store(true);
    });

    // Wait for initialization with timeout
    const auto timeout = std::chrono::seconds(timeoutSeconds);
    while (!initComplete.load() &&
           (std::chrono::steady_clock::now() - start) < timeout) {
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    if (!initComplete.load()) {
      spdlog::error("{} initialization timed out after {}s", componentName,
                    timeoutSeconds);
      // Detach the thread since we can't safely terminate it
      initThread.detach();
      return false;
    }

    // Join the thread and check results
    initThread.join();

    if (initException) {
      std::rethrow_exception(initException);
    }

    if (!initResult.load()) {
      spdlog::error("{} initialization failed", componentName);
      return false;
    }

    cleanupFunctions.push_back(cleanupFunc);
    auto end = std::chrono::steady_clock::now();
    auto duration =
        std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    spdlog::info("{} initialized successfully in {}ms", componentName,
                 duration.count());
    return true;

  } catch (const std::exception &e) {
    spdlog::error("{} initialization threw exception: {}", componentName,
                  e.what());
    return false;
  }
}

/**
 * @brief Initializes the emulator.
 * @param window SDL window.
 * @param vulkanContext Shared Vulkan context.
 * @return True on success, false otherwise.
 */
bool PS4Emulator::Initialize(SDL_Window *window, VulkanContext *vulkanContext) {
  auto start = std::chrono::steady_clock::now();
  spdlog::info("=== PS4 Emulator Initialization Started ===");

  try {
    // --- ADD: Validate system requirements before initialization ---
    spdlog::info("Step 1/10: Validating system requirements...");
    if (!ValidateSystemRequirements()) {
      spdlog::error("System requirements not met for PS4 emulation");
      return false;
    }

    // --- ADD: Initialize with proper error recovery ---
    std::vector<std::function<void()>> cleanupFunctions;

    // Create components first
    spdlog::info("Step 2/10: Creating component instances...");
    m_window = window;
    m_mmu = std::make_unique<PS4MMU>();
    m_tlb = std::make_unique<ps4::TLB>();
    m_os = std::make_unique<OrbisOS>(*this);
    m_filesystem = std::make_unique<PS4Filesystem>(*this);
    m_syscallHandler = std::make_unique<SyscallHandler>(*this);
    m_controllerManager = std::make_unique<PS4ControllerManager>(*m_mmu);
    m_audio = std::make_unique<PS4Audio>(*m_mmu);
    m_tsc = std::make_unique<PS4TSC>();
    m_shaderTranslator = std::make_unique<GNMShaderTranslator>();
    m_gpu = std::make_unique<PS4GPU>(*m_mmu, std::move(m_shaderTranslator),
                                     nullptr, vulkanContext, window);
    m_commandProcessor = std::make_unique<CommandProcessor>(*m_mmu, *m_gpu);
    m_tileManager = std::make_unique<TileManager>(m_gpu->GetMutableGNMState(),
                                                  *m_commandProcessor);
    m_fiberManager = std::make_unique<ps4::FiberManager>();
    m_trophyManager = std::make_unique<ps4::TrophyManager>();
    m_zlibWrapper = std::make_unique<ps4::ZlibWrapper>();
    m_pkgInstaller =
        std::make_unique<ps4::PKGInstaller>(m_filesystem.get(), this);
    for (uint32_t i = 0; i < 8; ++i) {
      auto cpu = std::make_unique<x86_64::X86_64CPU>(*this, *m_mmu, i);
      m_jitCompilers.emplace_back(
          std::make_unique<x86_64::X86_64JITCompiler>(cpu.get()));
      m_cpus.push_back(std::move(cpu));
    }
    m_interruptHandler =
        std::make_unique<x86_64::InterruptHandler>(*m_cpus[0], *m_mmu);
    m_ioManager =
        std::make_unique<x86_64::IOManager>(*this, *m_interruptHandler);

    // Initialize components with rollback capability
    spdlog::info("Step 3/10: Initializing core components...");
    auto rollbackAll = [&]() {
      spdlog::warn("Rolling back component initialization due to failure");
      for (auto it = cleanupFunctions.rbegin(); it != cleanupFunctions.rend();
           ++it) {
        (*it)();
      }
    };

    if (!InitializeComponentWithRollback(
            [this]() { return m_mmu->Initialize(); },
            [this]() { /* MMU has no shutdown */ }, cleanupFunctions, "MMU")) {
      rollbackAll();
      return false;
    }
    if (!InitializeComponentWithRollback(
            [this]() { return m_tsc->Initialize(); },
            [this]() { /* TSC has no shutdown */ }, cleanupFunctions, "TSC")) {
      rollbackAll();
      return false;
    }
    if (!InitializeComponentWithTimeout(
            [this]() { return m_filesystem->Initialize(); },
            [this]() { /* Filesystem has no shutdown */ }, cleanupFunctions,
            "Filesystem", 45)) {
      rollbackAll();
      return false;
    }
    if (!InitializeComponentWithRollback(
            [this]() { return m_os->Initialize(); },
            [this]() { /* OS has no shutdown */ }, cleanupFunctions, "OS")) {
      rollbackAll();
      return false;
    }
    if (!InitializeComponentWithRollback(
            [this]() { return m_ioManager->InitializeStandardDevices(); },
            [this]() { /* IOManager has no shutdown */ }, cleanupFunctions,
            "IOManager")) {
      rollbackAll();
      return false;
    }
    if (!InitializeComponentWithTimeout(
            [this]() { return m_fiberManager->Initialize(); },
            [this]() { /* FiberManager has no shutdown */ }, cleanupFunctions,
            "FiberManager", 15)) {
      rollbackAll();
      return false;
    }
    if (!InitializeComponentWithRollback(
            [this]() { return m_trophyManager->Initialize("default_user"); },
            [this]() { /* TrophyManager has no shutdown */ }, cleanupFunctions,
            "TrophyManager")) {
      rollbackAll();
      return false;
    }

    spdlog::info("Step 4/10: Initializing graphics subsystem...");
    if (!InitializeComponentWithTimeout(
            [this]() { return m_tileManager->Initialize(); },
            [this]() { /* TileManager has no shutdown */ }, cleanupFunctions,
            "TileManager", 10)) {
      rollbackAll();
      return false;
    }

    if (!InitializeComponentWithTimeout(
            [this]() { return m_gpu->Initialize(); },
            [this]() { /* GPU has no shutdown */ }, cleanupFunctions, "GPU",
            60)) {
      rollbackAll();
      return false;
    }

    spdlog::info("Step 5/10: Initializing input and audio subsystems...");
    if (!InitializeComponentWithRollback(
            [this]() { return m_controllerManager->Initialize(); },
            [this]() { /* ControllerManager has no shutdown */ },
            cleanupFunctions, "ControllerManager")) {
      rollbackAll();
      return false;
    }

    // Initialize audio with configuration check
    bool audioEnabled = IsAudioEnabled();
    spdlog::info("Audio initialization: enabled={}", audioEnabled);
    if (!InitializeComponentWithTimeout(
            [this, audioEnabled]() {
              return m_audio->Initialize(audioEnabled);
            },
            [this]() { /* Audio has no shutdown */ }, cleanupFunctions, "Audio",
            20)) {
      rollbackAll();
      return false;
    }

    // Initialize CPU cores with timeout protection
    spdlog::info("Step 6/10: Initializing {} CPU cores...", m_cpus.size());
    for (size_t i = 0; i < m_cpus.size(); ++i) {
      // CRITICAL: Add bounds check for CPU vector access
      if (i >= m_cpus.size()) {
        spdlog::error("PS4Emulator: CPU index {} out of bounds (size={})", i, m_cpus.size());
        rollbackAll();
        return false;
      }
      const auto &cpu = m_cpus[i];
      std::string cpuName = fmt::format("CPU[{}]", i);
      if (!InitializeComponentWithTimeout([&]() { return cpu->Initialize(); },
                                          [&]() { /* CPU has no shutdown */ },
                                          cleanupFunctions, cpuName, 20)) {
        rollbackAll();
        return false;
      }
    }

    spdlog::info("Step 7/10: Initializing interrupt handler...");
    if (!InitializeComponentWithTimeout(
            [this]() {
              m_interruptHandler->Initialize();
              return true;
            },
            [this]() { /* InterruptHandler has no shutdown */ },
            cleanupFunctions, "InterruptHandler", 10)) {
      rollbackAll();
      return false;
    }

    // Update final state
    spdlog::info("Step 8/10: Finalizing emulator state...");
    {
      EMULATOR_LOCK(m_emulatorMutex);
      MemoryDiagnostics::GetInstance().ResetMetrics();
      x86_64::CPUDiagnostics::GetInstance().ResetMetrics();
      x86_64::JITDiagnostics::GetInstance().ResetMetrics();
      m_stats = Stats();
      m_running = false;
      m_paused = false;
    }

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    auto latencyMs =
        std::chrono::duration_cast<std::chrono::milliseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("=== PS4 Emulator Initialization Complete ===");
    spdlog::info("Total initialization time: {}ms ({}us)", latencyMs, latency);
    return true;

  } catch (const std::exception &e) {
    spdlog::error("PS4Emulator initialization failed with exception: {}",
                  e.what());
    // Ensure cleanup happens even on exception
    Shutdown();
    return false;
  }
}

/**
 * @brief Shuts down the emulator.
 */
void PS4Emulator::Shutdown() {
  auto start = std::chrono::steady_clock::now();

  {
    EMULATOR_LOCK(m_emulatorMutex);
    try {
      Stop();
      if (m_resourceMonitorThread.joinable()) {
        m_resourceMonitorThread.join();
      }
      m_zlibWrapper.reset();
      m_trophyManager.reset();
      m_fiberManager.reset();
      m_commandProcessor.reset();
      m_gpu.reset();
      m_tileManager.reset();
      m_shaderTranslator.reset();
      m_tsc.reset();
      m_audio.reset();
      m_controllerManager.reset();
      m_syscallHandler.reset();
      m_filesystem.reset();
      m_os.reset();
      m_ioManager.reset();
      m_interruptHandler.reset();
      m_jitCompilers.clear();
      m_cpus.clear();
      m_tlb.reset();
      m_mmu.reset();
      m_breakpoints.clear();
      m_loadedModules.clear();
      m_stats = Stats();
      m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    } catch (const std::exception &e) {
      spdlog::error("PS4Emulator shutdown failed: {}", e.what());
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    }
  } // Lock automatically released here

  auto end = std::chrono::steady_clock::now();
  auto latency =
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  spdlog::info("PS4Emulator shutdown");
}

/**
 * @brief Loads a game executable or PKG file.
 * @param path Path to the executable or PKG file.
 * @return True on success, false otherwise.
 */
bool PS4Emulator::LoadGame(const std::string &path) {
  auto start = std::chrono::steady_clock::now();

  try {
    // Check file extension to determine file type
    std::filesystem::path filePath(path);
    std::string extension = filePath.extension().string();
    std::transform(extension.begin(), extension.end(), extension.begin(),
                   ::tolower);

    if (extension == ".pkg") {
      // Handle PKG file installation
      spdlog::info("LoadGame: Detected PKG file, installing: {}", path);

      if (!m_pkgInstaller) {
        spdlog::error("LoadGame: PKG installer not initialized");
        EMULATOR_LOCK(m_emulatorMutex);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }

      // Install the PKG file
      spdlog::info("LoadGame: Installing PKG file: {}", path);
      if (!m_pkgInstaller->InstallPKG(path)) {
        spdlog::error("LoadGame: Failed to install PKG: {}", path);
        EMULATOR_LOCK(m_emulatorMutex);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }
      spdlog::info("LoadGame: PKG installation completed successfully");

      // Get PKG info to find the main executable
      std::string contentId, version, title;
      if (!m_pkgInstaller->GetPKGInfo(path, contentId, version, title)) {
        spdlog::error("LoadGame: Failed to get PKG info: {}", path);
        EMULATOR_LOCK(m_emulatorMutex);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }

      // Get the actual install path from the PKG installer
      std::string actualInstallPath = m_pkgInstaller->GetInstallPath(contentId);
      spdlog::info("LoadGame: PKG installer reports install path: '{}'",
                   actualInstallPath);

      // Try to find and load the main executable from the installed package
      std::string installPath;
      if (!actualInstallPath.empty()) {
        installPath = actualInstallPath + "/eboot.bin";
      } else {
        installPath = "/mnt/sandbox/pfsmnt/" + contentId + "/eboot.bin";
      }

      spdlog::info("LoadGame: Looking for main executable at: {}", installPath);

      // Check if file exists using filesystem
      std::ifstream testFile(installPath);
      if (!testFile.good()) {
        spdlog::warn(
            "LoadGame: Main executable not found at {}, trying fallback",
            installPath);
        // Fallback to alternative path
        installPath = "/app0/eboot.bin";
      }
      testFile.close();

      std::ifstream testFile2(installPath);
      if (!testFile2.good()) {
        spdlog::error("LoadGame: Cannot find main executable after PKG "
                      "installation at {} or fallback path",
                      installPath);
        EMULATOR_LOCK(m_emulatorMutex);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }

      spdlog::info("LoadGame: Found main executable at: {}", installPath);

      // Load the main executable using ELF loader directly with file path
      LoadedElf elf;
      ElfLoader loader(*this);
      if (!loader.Load(installPath, elf, false)) {
        spdlog::error("LoadGame: Failed to load main executable from PKG: {}",
                      installPath);
        EMULATOR_LOCK(m_emulatorMutex);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }

      // Update emulator state
      {
        EMULATOR_LOCK(m_emulatorMutex);
        m_os->SceCreateProcess(installPath);
        m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
      } // Lock automatically released here

      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
      spdlog::info("LoadGame: PKG installed and loaded {} in {}us", path,
                   latency);
      return true;

    } else {
      // Handle regular ELF/BIN files
      spdlog::info("LoadGame: Loading ELF/BIN file: {}", path);

      LoadedElf elf;
      ElfLoader loader(*this);
      if (!loader.Load(path, elf, false)) {
        spdlog::error("LoadGame: Failed to load ELF: {}", path);
        EMULATOR_LOCK(m_emulatorMutex);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }

      // Only acquire emulator mutex for updating internal state
      {
        EMULATOR_LOCK(m_emulatorMutex);
        m_os->SceCreateProcess(path);
        m_filesystem->MountDirectory(
            std::filesystem::path(path).parent_path().wstring());
        m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
      } // Lock automatically released here

      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
      spdlog::info("LoadGame: Loaded {} in {}us", path, latency);
      return true;
    }
  } catch (const std::exception &e) {
    spdlog::error("LoadGame: Failed to load {}: {}", path, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Starts the emulator execution.
 */
void PS4Emulator::Start() {
  auto start = std::chrono::steady_clock::now();

  {
    EMULATOR_LOCK(m_emulatorMutex);
    if (m_running) {
      spdlog::warn("Start: Emulator already running");
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return;
    }
    m_running = true;
    m_paused = false;
    m_coreThreads.clear();
  }

  try {
    // Start core threads first
    spdlog::info("Starting {} CPU core threads", m_cpus.size());
    for (uint32_t i = 0; i < m_cpus.size(); i++) {
      m_coreThreads.emplace_back(&PS4Emulator::CoreThread, this, i);
      spdlog::debug("Started core thread {}", i);
    }

    // Add delay to let threads initialize
    std::this_thread::sleep_for(std::chrono::milliseconds(10));

    // Set scheduling policy with timeout protection
    spdlog::info("Setting fiber scheduling policy to PRIORITY");
    try {
      auto policyStart = std::chrono::steady_clock::now();
      m_fiberManager->SetSchedulingPolicy(SchedulingPolicy::PRIORITY);
      auto policyEnd = std::chrono::steady_clock::now();
      auto policyDuration =
          std::chrono::duration_cast<std::chrono::milliseconds>(policyEnd -
                                                                policyStart);
      spdlog::info("Scheduling policy set in {}ms", policyDuration.count());
    } catch (const std::exception &e) {
      spdlog::error("Failed to set scheduling policy: {}", e.what());
      // Continue with default policy instead of failing completely
    }

    // Start resource monitor thread
    spdlog::debug("Starting resource monitor thread");
    m_resourceMonitorThread =
        std::thread(&PS4Emulator::ResourceMonitorThread, this);

    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Emulator started successfully with {} cores in {}us",
                 m_cpus.size(), latency);
  } catch (const std::exception &e) {
    spdlog::error("Start emulator failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);

    // Clean up any partially started threads
    {
      EMULATOR_LOCK(m_emulatorMutex);
      m_running = false;
    }

    // Wait for threads to finish
    for (auto &thread : m_coreThreads) {
      if (thread.joinable()) {
        thread.join();
      }
    }
    m_coreThreads.clear();
  }
}

/**
 * @brief Pauses the emulator execution.
 */
void PS4Emulator::Pause() {
  auto start = std::chrono::steady_clock::now();
  EMULATOR_LOCK(m_emulatorMutex);
  try {
    if (!m_running) {
      spdlog::warn("Pause: Emulator not running");
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return;
    }
    m_paused = true;
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Emulator paused");
  } catch (const std::exception &e) {
    spdlog::error("Pause emulator failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Stops the emulator execution.
 */
void PS4Emulator::Stop() {
  auto start = std::chrono::steady_clock::now();

  {
    EMULATOR_LOCK(m_emulatorMutex);
    if (!m_running) {
      spdlog::warn("Stop: Emulator not running");
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return;
    }
    m_running = false;
    m_paused = false;
  }

  try {
    for (auto &thread : m_coreThreads) {
      if (thread.joinable()) {
        thread.join();
      }
    }

    {
      EMULATOR_LOCK(m_emulatorMutex);
      m_coreThreads.clear();
    }
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Emulator stopped");
  } catch (const std::exception &e) {
    spdlog::error("Stop emulator failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Resumes the emulator execution.
 */
void PS4Emulator::Resume() {
  auto start = std::chrono::steady_clock::now();

  bool shouldStart = false;
  {
    EMULATOR_LOCK(m_emulatorMutex);
    if (!m_running) {
      spdlog::warn("Resume: Emulator not running, starting instead");
      shouldStart = true;
    }
  } // Lock released here

  if (shouldStart) {
    Start();
    return;
  }

  {
    EMULATOR_LOCK(m_emulatorMutex);
    try {
      if (!m_paused) {
        spdlog::warn("Resume: Emulator not paused");
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return;
      }
      m_paused = false;
      m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
      spdlog::info("Emulator resumed");
    } catch (const std::exception &e) {
      spdlog::error("Resume emulator failed: {}", e.what());
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    }
  }
}

/**
 * @brief Resets the emulator state.
 */
void PS4Emulator::Reset() {
  auto start = std::chrono::steady_clock::now();

  try {
    // Stop emulation first
    Stop();

    EMULATOR_LOCK(m_emulatorMutex);

    // Reset all CPU cores
    for (auto &cpu : m_cpus) {
      cpu->ResetState();
    }

    // Reset components that have Reset methods
    // Note: Only reset components that actually implement Reset method
    // Other components will be reset through their normal initialization
    // process

    // Reset statistics and clear internal state
    // Most components don't have Reset methods, so we'll rely on
    // reinitialization when needed

    // Reset statistics
    m_stats = Stats();
    MemoryDiagnostics::GetInstance().ResetMetrics();
    x86_64::CPUDiagnostics::GetInstance().ResetMetrics();
    x86_64::JITDiagnostics::GetInstance().ResetMetrics();

    // Clear breakpoints and loaded modules
    m_breakpoints.clear();
    m_loadedModules.clear();

    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Emulator reset completed");
  } catch (const std::exception &e) {
    spdlog::error("Reset emulator failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Adds a breakpoint.
 * @param address Breakpoint address.
 * @param condition Optional condition.
 */
void PS4Emulator::AddBreakpoint(uint64_t address,
                                const std::string &condition) {
  auto start = std::chrono::steady_clock::now();
  EMULATOR_LOCK(m_emulatorMutex);
  try {
    m_breakpoints.push_back({address, true, condition});
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Added breakpoint at 0x{:x}, condition='{}'", address,
                 condition);
  } catch (const std::exception &e) {
    spdlog::error("Add breakpoint failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Removes a breakpoint.
 * @param address Breakpoint address.
 */
void PS4Emulator::RemoveBreakpoint(uint64_t address) {
  auto start = std::chrono::steady_clock::now();
  EMULATOR_LOCK(m_emulatorMutex);
  try {
    m_breakpoints.erase(std::remove_if(m_breakpoints.begin(),
                                       m_breakpoints.end(),
                                       [address](const Breakpoint &bp) {
                                         return bp.address == address;
                                       }),
                        m_breakpoints.end());
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Removed breakpoint at 0x{:x}", address);
  } catch (const std::exception &e) {
    spdlog::error("Remove breakpoint failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Adds a loaded module.
 * @param name Module name.
 * @param moduleId Module ID.
 */
void PS4Emulator::AddLoadedModule(const std::string &name, const int moduleId) {
  auto start = std::chrono::steady_clock::now();
  EMULATOR_LOCK(m_emulatorMutex);
  try {
    m_loadedModules[name] = moduleId;
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Added loaded module: {} (ID {})", name, moduleId);
  } catch (const std::exception &e) {
    spdlog::error("Add loaded module failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

// --- IMPROVE: ExecuteCycle with better error handling and performance
// monitoring ---
void PS4Emulator::ExecuteCycle() {
  static thread_local uint64_t cycleCount = 0;
  static thread_local auto lastPerfCheck = std::chrono::steady_clock::now();

  try {
    // Performance monitoring
    if (++cycleCount % 1000000 == 0) {
      auto now = std::chrono::steady_clock::now();
      auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                         now - lastPerfCheck)
                         .count();
      if (elapsed > 1000) { // Every second
        double cyclesPerSecond = (cycleCount * 1000.0) / elapsed;
        spdlog::trace("Performance: {:.2f} M cycles/sec",
                      cyclesPerSecond / 1000000.0);
        cycleCount = 0;
        lastPerfCheck = now;
      }
    }

    // Existing cycle execution with additional error handling
    uint64_t currentCycle = m_tsc->GetTSC();

    // --- ADD: Detect cycle overflow/wraparound ---
    static thread_local uint64_t lastCycle = 0;
    if (currentCycle < lastCycle &&
        (lastCycle - currentCycle) > UINT64_MAX / 2) {
      spdlog::warn("TSC cycle wraparound detected");
      HandleTSCWrapAround();
    }
    lastCycle = currentCycle;

    // Execute with timeout protection
    auto startTime = std::chrono::steady_clock::now();

    // Original execution logic...
    for (size_t i = 0; i < m_cpus.size(); ++i) {
      // CRITICAL: Add bounds check for CPU vector access
      if (i >= m_cpus.size()) {
        spdlog::error("PS4Emulator: CPU index {} out of bounds during execution (size={})",
                     i, m_cpus.size());
        break;
      }
      if (m_cpus[i] == nullptr) {
        spdlog::error("PS4Emulator: CPU {} is null during execution", i);
        continue;
      }
      m_cpus[i]->ExecuteCycle();
    }

    // --- ADD: Detect hung execution ---
    auto executionTime = std::chrono::steady_clock::now() - startTime;
    if (executionTime >
        std::chrono::milliseconds(100)) { // 100ms is too long for one cycle
      spdlog::warn(
          "Long execution cycle detected: {} ms",
          std::chrono::duration_cast<std::chrono::milliseconds>(executionTime)
              .count());
      m_enhancedStats->slowCycles++;
    }

  } catch (const std::exception &e) {
    spdlog::error("ExecuteCycle exception: {}", e.what());
    m_enhancedStats->cycleErrors++;

    // --- ADD: Attempt recovery ---
    if (m_enhancedStats->cycleErrors > 100) {
      spdlog::critical("Too many cycle errors, stopping emulation");
      Stop();
    }
  }
}

/**
 * @brief Core execution thread for a CPU.
 * @param coreId CPU core index.
 */
void PS4Emulator::CoreThread(uint32_t coreId) {
  // CRITICAL: Add bounds check for CPU vector access
  if (coreId >= m_cpus.size()) {
    spdlog::error("PS4Emulator: CoreThread coreId {} out of bounds (size={})",
                 coreId, m_cpus.size());
    return;
  }
  if (m_cpus[coreId] == nullptr) {
    spdlog::error("PS4Emulator: CPU {} is null in CoreThread", coreId);
    return;
  }
  auto &cpu = *m_cpus[coreId];
  spdlog::info("CoreThread started for core {}", coreId);
  while (m_running) {
    try {
      if (m_paused) {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        continue;
      }

      // Check breakpoints with minimal locking
      bool shouldBreak = false;
      {
        ORDERED_SHARED_LOCK(m_emulatorMutex, ps4::LockLevel::EMULATOR,
                            "EmulatorMutex");
        uint64_t currentRIP = cpu.GetRegister(x86_64::Register::RIP);

        for (const auto &bp : m_breakpoints) {
          if (bp.enabled && currentRIP == bp.address) {
            bool conditionMet = true;
            if (!bp.condition.empty()) {
              conditionMet =
                  this->EvaluateBreakpointCondition(bp.condition, cpu);
            }
            if (conditionMet) {
              shouldBreak = true;
              m_stats.breakpointHits.fetch_add(1, std::memory_order_relaxed);
              spdlog::info(
                  "Breakpoint hit at 0x{:x} on core {}, condition='{}' "
                  "evaluated to true",
                  bp.address, coreId, bp.condition);
              break;
            } else {
              spdlog::trace(
                  "Breakpoint at 0x{:x} condition='{}' evaluated to false",
                  bp.address, bp.condition);
            }
          }
        }
      }

      if (shouldBreak) {
        m_paused = true;
        continue;
      }

      if (!m_paused) {
        ExecuteCycle(); // Use the improved ExecuteCycle method

        m_stats.instructionsExecuted.fetch_add(1, std::memory_order_relaxed);
        m_stats.totalCycles.fetch_add(cpu.GetPipeline().GetStats().cycles,
                                      std::memory_order_relaxed);
        m_ioManager->Cycle();

        // Add timeout protection for fiber scheduling to prevent hangs
        try {
          auto scheduleStart = std::chrono::steady_clock::now();
          m_fiberManager->ScheduleNextFiber();
          auto scheduleEnd = std::chrono::steady_clock::now();
          auto scheduleDuration =
              std::chrono::duration_cast<std::chrono::milliseconds>(
                  scheduleEnd - scheduleStart);

          // Log warning if scheduling takes too long
          if (scheduleDuration.count() > 50) {
            spdlog::warn("Fiber scheduling took {}ms on core {}",
                         scheduleDuration.count(), coreId);
          }
        } catch (const std::exception &e) {
          spdlog::error("Fiber scheduling failed on core {}: {}", coreId,
                        e.what());
          // Continue execution even if fiber scheduling fails
          m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        }

        m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
        MemoryDiagnostics::GetInstance().UpdateMetrics();
        x86_64::CPUDiagnostics::GetInstance().UpdateMetrics();
        x86_64::JITDiagnostics::GetInstance().UpdateMetrics();
      }

      if (m_stats.instructionsExecuted.load(std::memory_order_relaxed) == 0) {
        std::this_thread::sleep_for(std::chrono::microseconds(100));
      }
    } catch (const std::exception &e) {
      spdlog::error("CoreThread {} failed: {}", coreId, e.what());
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    }
  }
  spdlog::info("CoreThread stopped for core {}", coreId);
}

/**
 * @brief Loads a module.
 * @param path Module path.
 * @param outElf Output LoadedElf structure.
 */
void PS4Emulator::LoadModule(const std::string &path, LoadedElf &outElf) {
  auto start = std::chrono::steady_clock::now();
  try {
    ElfLoader loader(*this);
    if (!loader.Load(path, outElf, true)) {
      spdlog::error("LoadModule: Failed to load module: {}", path);
      throw PS4EmulatorException("Failed to load module");
    }
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("LoadModule failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    throw;
  }
}

/**
 * @brief Saves the emulator state.
 * @param path File path.
 */
void PS4Emulator::SaveState(const std::string &path) {
  auto start = std::chrono::steady_clock::now();

  std::ofstream out;
  {
    EMULATOR_LOCK(m_emulatorMutex);
    out.open(path, std::ios::binary);
    if (!out) {
      spdlog::error("SaveState: Failed to open file: {}", path);
      throw PS4EmulatorException("Failed to save state");
    }
  }

  try {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));

    for (auto &cpu : m_cpus) {
      cpu->SaveState(out);
    }
    m_mmu->SaveState(out);
    m_tlb->SaveState(out);
    for (auto &jit : m_jitCompilers) {
      jit->SaveState(out);
    }
    m_gpu->SaveState(out);
    m_tsc->SaveState(out);
    m_interruptHandler->SaveState(out);
    m_ioManager->SaveState(out);
    m_controllerManager->SaveState(out);
    m_audio->SaveState(out);
    m_os->SaveState(out);
    m_filesystem->SaveState(out);
    m_syscallHandler->SaveState(out);
    m_commandProcessor->SaveState(out);
    m_fiberManager->SaveState(out);
    m_trophyManager->SaveState(out);
    m_zlibWrapper->SaveState(out);
    uint64_t breakpointCount = m_breakpoints.size();
    out.write(reinterpret_cast<const char *>(&breakpointCount),
              sizeof(breakpointCount));
    for (const auto &bp : m_breakpoints) {
      out.write(reinterpret_cast<const char *>(&bp.address),
                sizeof(bp.address));
      out.write(reinterpret_cast<const char *>(&bp.enabled),
                sizeof(bp.enabled));
      uint32_t condLen = static_cast<uint32_t>(bp.condition.size());
      out.write(reinterpret_cast<const char *>(&condLen), sizeof(condLen));
      out.write(bp.condition.data(), condLen);
    }
    uint64_t moduleCount = m_loadedModules.size();
    out.write(reinterpret_cast<const char *>(&moduleCount),
              sizeof(moduleCount));
    for (const auto &[name, id] : m_loadedModules) {
      uint32_t nameLen = static_cast<uint32_t>(name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(name.data(), nameLen);
      out.write(reinterpret_cast<const char *>(&id), sizeof(id));
    }
    {
      ORDERED_SHARED_LOCK(m_emulatorMutex, ps4::LockLevel::EMULATOR,
                          "EmulatorMutex");
      uint64_t instructionsExecuted = m_stats.instructionsExecuted.load();
      uint64_t totalCycles = m_stats.totalCycles.load();
      uint64_t totalLatencyUs = m_stats.totalLatencyUs.load();
      uint64_t cacheHits = m_stats.cacheHits.load();
      uint64_t cacheMisses = m_stats.cacheMisses.load();
      uint64_t breakpointHits = m_stats.breakpointHits.load();

      out.write(reinterpret_cast<const char *>(&instructionsExecuted),
                sizeof(instructionsExecuted));
      out.write(reinterpret_cast<const char *>(&totalCycles),
                sizeof(totalCycles));
      out.write(reinterpret_cast<const char *>(&totalLatencyUs),
                sizeof(totalLatencyUs));
      out.write(reinterpret_cast<const char *>(&cacheHits), sizeof(cacheHits));
      out.write(reinterpret_cast<const char *>(&cacheMisses),
                sizeof(cacheMisses));
      out.write(reinterpret_cast<const char *>(&breakpointHits),
                sizeof(breakpointHits));
      out.write(reinterpret_cast<const char *>(&m_running), sizeof(m_running));
      out.write(reinterpret_cast<const char *>(&m_paused), sizeof(m_paused));
    }

    if (!out.good()) {
      throw std::runtime_error("Failed to write emulator state");
    }

    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);

    spdlog::info("SaveState: Saved to {}", path);
  } catch (const std::exception &e) {
    spdlog::error("SaveState failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Loads the emulator state.
 * @param path File path.
 */
void PS4Emulator::LoadState(const std::string &path) {
  auto start = std::chrono::steady_clock::now();

  std::ifstream in;
  {
    EMULATOR_LOCK(m_emulatorMutex);
    in.open(path, std::ios::binary);
    if (!in) {
      spdlog::error("LoadState: Failed to open file: {}", path);
      throw PS4EmulatorException("Failed to load state");
    }
  }

  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      spdlog::error("Unsupported emulator state version: {}", version);
      throw std::runtime_error("Invalid emulator state version");
    }

    for (auto &cpu : m_cpus) {
      cpu->LoadState(in);
    }
    m_mmu->LoadState(in);
    m_tlb->LoadState(in);
    for (auto &jit : m_jitCompilers) {
      jit->LoadState(in);
    }
    m_gpu->LoadState(in);
    m_tsc->LoadState(in);
    m_interruptHandler->LoadState(in);
    m_ioManager->LoadState(in);
    m_controllerManager->LoadState(in);
    m_audio->LoadState(in);
    m_os->LoadState(in);
    m_filesystem->LoadState(in);
    m_syscallHandler->LoadState(in);
    m_commandProcessor->LoadState(in);
    m_fiberManager->LoadState(in);
    m_trophyManager->LoadState(in);
    m_zlibWrapper->LoadState(in);
    uint64_t breakpointCount;
    in.read(reinterpret_cast<char *>(&breakpointCount),
            sizeof(breakpointCount));
    m_breakpoints.resize(breakpointCount);
    for (auto &bp : m_breakpoints) {
      in.read(reinterpret_cast<char *>(&bp.address), sizeof(bp.address));
      in.read(reinterpret_cast<char *>(&bp.enabled), sizeof(bp.enabled));
      uint32_t condLen;
      in.read(reinterpret_cast<char *>(&condLen), sizeof(condLen));
      bp.condition.resize(condLen);
      in.read(bp.condition.data(), condLen);
    }
    m_loadedModules.clear();
    uint64_t moduleCount;
    in.read(reinterpret_cast<char *>(&moduleCount), sizeof(moduleCount));
    for (uint64_t i = 0; i < moduleCount && in.good(); ++i) {
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      int id;
      in.read(reinterpret_cast<char *>(&id), sizeof(id));
      m_loadedModules[name] = id;
    }

    {
      EMULATOR_LOCK(m_emulatorMutex);
      uint64_t instructionsExecuted, totalCycles, totalLatencyUs, cacheHits,
          cacheMisses, breakpointHits;

      in.read(reinterpret_cast<char *>(&instructionsExecuted),
              sizeof(instructionsExecuted));
      in.read(reinterpret_cast<char *>(&totalCycles), sizeof(totalCycles));
      in.read(reinterpret_cast<char *>(&totalLatencyUs),
              sizeof(totalLatencyUs));
      in.read(reinterpret_cast<char *>(&cacheHits), sizeof(cacheHits));
      in.read(reinterpret_cast<char *>(&cacheMisses), sizeof(cacheMisses));
      in.read(reinterpret_cast<char *>(&breakpointHits),
              sizeof(breakpointHits));
      in.read(reinterpret_cast<char *>(&m_running), sizeof(m_running));
      in.read(reinterpret_cast<char *>(&m_paused), sizeof(m_paused));

      if (!in.good()) {
        throw std::runtime_error("Failed to read emulator state");
      }

      m_stats.instructionsExecuted.store(instructionsExecuted);
      m_stats.totalCycles.store(totalCycles);
      m_stats.totalLatencyUs.store(totalLatencyUs);
      m_stats.cacheHits.store(cacheHits);
      m_stats.cacheMisses.store(cacheMisses);
      m_stats.breakpointHits.store(breakpointHits);
    }

    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("LoadState: Loaded from {}, latency={}us", path, latency);
  } catch (const std::exception &e) {
    spdlog::error("LoadState failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Retrieves emulator statistics.
 * @return Current statistics.
 */
PS4Emulator::Stats PS4Emulator::GetStats() const {
  auto start = std::chrono::steady_clock::now();
  ORDERED_SHARED_LOCK(m_emulatorMutex, ps4::LockLevel::EMULATOR,
                      "EmulatorMutex");
  try {
    Stats result;
    result.instructionsExecuted.store(m_stats.instructionsExecuted.load());
    result.totalCycles.store(m_stats.totalCycles.load());
    result.totalLatencyUs.store(m_stats.totalLatencyUs.load());
    result.cacheHits.store(m_stats.cacheHits.load());
    result.cacheMisses.store(m_stats.cacheMisses.load());
    result.breakpointHits.store(m_stats.breakpointHits.load());

    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    return result;
  } catch (const std::exception &e) {
    spdlog::error("Get emulator stats failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return m_stats;
  }
}

// --- ADD: Configuration validation ---
bool PS4Emulator::Reconfigure(uint32_t cpuCount,
                              const std::string &gpuSettings) {
  EMULATOR_LOCK(m_emulatorMutex);

  // Validate CPU count
  if (cpuCount == 0 ||
      cpuCount > 16) { // PS4 has 8 cores, but allow some flexibility
    spdlog::error("Invalid CPU count: {}. Must be between 1 and 16", cpuCount);
    return false;
  }

  // Validate GPU settings format
  auto ValidateGPUSettings = [](const std::string &settings) -> bool {
    // Simple validation: ensure it's a key=value; pair format
    std::stringstream ss(settings);
    std::string segment;
    while (std::getline(ss, segment, ';')) {
      if (segment.find('=') == std::string::npos && !segment.empty()) {
        return false;
      }
    }
    return true;
  };

  if (!gpuSettings.empty() && !ValidateGPUSettings(gpuSettings)) {
    spdlog::error("Invalid GPU settings format: {}", gpuSettings);
    return false;
  }

  // Store old configuration for rollback
  auto oldCpuCount = m_cpus.size();
  // auto oldGpuSettings = GetCurrentGPUSettings(); // This function would need
  // to be implemented

  try {
    Stop(); // Stop emulation before reconfiguring

    // Apply new configuration
    if (cpuCount != m_cpus.size()) {
      m_cpus.clear();
      m_jitCompilers.clear();
      for (uint32_t i = 0; i < cpuCount; ++i) {
        auto cpu = std::make_unique<x86_64::X86_64CPU>(*this, *m_mmu, i);
        m_jitCompilers.emplace_back(
            std::make_unique<x86_64::X86_64JITCompiler>(cpu.get()));
        m_cpus.push_back(std::move(cpu));
      }
      for (const auto &cpu : m_cpus) {
        if (!cpu->Initialize()) {
          throw std::runtime_error("Failed to re-initialize CPU cores");
        }
      }
    }

    // if (!gpuSettings.empty()) {
    //   if (!m_gpu->Configure(gpuSettings)) { // This function would need to be
    //   implemented
    //     // Rollback CPU changes if GPU configuration fails
    //     // ReconfigureCPUCores(oldCpuCount);
    //     throw std::runtime_error("Failed to configure GPU");
    //   }
    // }

    spdlog::info("Reconfiguration successful: {} CPUs, GPU: {}", cpuCount,
                 gpuSettings.empty() ? "unchanged" : gpuSettings);
    return true;

  } catch (const std::exception &e) {
    spdlog::error("Reconfiguration failed: {}. Rolling back changes.",
                  e.what());
    // Attempt to restore original configuration
    // ReconfigureCPUCores(oldCpuCount);
    // m_gpu.Configure(oldGpuSettings);
    return false;
  }
}

/**
 * @brief Enhanced expression parser for complex breakpoint conditions.
 * @details Supports boolean operators (&&, ||), parentheses, memory access
 * [address], arithmetic operations, and complex register comparisons.
 */
class BreakpointExpressionParser {
public:
  enum class TokenType {
    NUMBER,   // 123, 0x1234
    REGISTER, // rax, rbx, etc.
    MEMORY,   // [address] or [reg+offset]
    OPERATOR, // ==, !=, >, <, >=, <=, +, -, *, /, %
    LOGICAL,  // &&, ||
    LPAREN,   // (
    RPAREN,   // )
    LBRACKET, // [
    RBRACKET, // ]
    END       // End of input
  };

  struct Token {
    TokenType type;
    std::string value;
    uint64_t numValue = 0;
    x86_64::Register reg = x86_64::Register::NONE;
  };

private:
  std::string m_expression;
  x86_64::X86_64CPU &m_cpu;
  PS4MMU &m_mmu;
  std::vector<Token> m_tokens;
  size_t m_currentToken = 0;

public:
  BreakpointExpressionParser(const std::string &expr, x86_64::X86_64CPU &cpu,
                             PS4MMU &mmu)
      : m_expression(expr), m_cpu(cpu), m_mmu(mmu) {
    Tokenize();
  }

  bool Parse() {
    m_currentToken = 0;
    return ParseLogicalOr();
  }

private:
  void Tokenize() {
    m_tokens.clear();
    size_t pos = 0;

    while (pos < m_expression.length()) {
      // Skip whitespace
      while (pos < m_expression.length() && std::isspace(m_expression[pos])) {
        pos++;
      }

      if (pos >= m_expression.length())
        break;

      // Parse tokens
      if (m_expression[pos] == '(') {
        m_tokens.push_back({TokenType::LPAREN, "("});
        pos++;
      } else if (m_expression[pos] == ')') {
        m_tokens.push_back({TokenType::RPAREN, ")"});
        pos++;
      } else if (m_expression[pos] == '[') {
        m_tokens.push_back({TokenType::LBRACKET, "["});
        pos++;
      } else if (m_expression[pos] == ']') {
        m_tokens.push_back({TokenType::RBRACKET, "]"});
        pos++;
      } else if (pos + 1 < m_expression.length()) {
        // Check for two-character operators
        std::string twoChar = m_expression.substr(pos, 2);
        if (twoChar == "==" || twoChar == "!=" || twoChar == ">=" ||
            twoChar == "<=" || twoChar == "&&" || twoChar == "||") {
          TokenType type = (twoChar == "&&" || twoChar == "||")
                               ? TokenType::LOGICAL
                               : TokenType::OPERATOR;
          m_tokens.push_back({type, twoChar});
          pos += 2;
        } else {
          // Single character operators
          char ch = m_expression[pos];
          if (ch == '>' || ch == '<' || ch == '+' || ch == '-' || ch == '*' ||
              ch == '/' || ch == '%') {
            m_tokens.push_back({TokenType::OPERATOR, std::string(1, ch)});
            pos++;
          } else if (std::isdigit(ch) ||
                     (ch == '0' && pos + 1 < m_expression.length() &&
                      (m_expression[pos + 1] == 'x' ||
                       m_expression[pos + 1] == 'X'))) {
            // Parse number
            pos += ParseNumber(pos);
          } else if (std::isalpha(ch)) {
            // Parse register name
            pos += ParseRegister(pos);
          } else {
            // Unknown character, skip
            pos++;
          }
        }
      } else {
        // Single character at end
        char ch = m_expression[pos];
        if (ch == '>' || ch == '<' || ch == '+' || ch == '-' || ch == '*' ||
            ch == '/' || ch == '%') {
          m_tokens.push_back({TokenType::OPERATOR, std::string(1, ch)});
        }
        pos++;
      }
    }

    m_tokens.push_back({TokenType::END, ""});
  }

  size_t ParseNumber(size_t start) {
    size_t pos = start;
    std::string numStr;

    // Check for hex prefix
    if (pos + 1 < m_expression.length() && m_expression[pos] == '0' &&
        (m_expression[pos + 1] == 'x' || m_expression[pos + 1] == 'X')) {
      numStr += m_expression.substr(pos, 2);
      pos += 2;
      // Parse hex digits
      while (pos < m_expression.length() && std::isxdigit(m_expression[pos])) {
        numStr += m_expression[pos++];
      }
    } else {
      // Parse decimal digits
      while (pos < m_expression.length() && std::isdigit(m_expression[pos])) {
        numStr += m_expression[pos++];
      }
    }

    Token token;
    token.type = TokenType::NUMBER;
    token.value = numStr;

    if (numStr.substr(0, 2) == "0x" || numStr.substr(0, 2) == "0X") {
      token.numValue = std::stoull(numStr, nullptr, 16);
    } else {
      token.numValue = std::stoull(numStr, nullptr, 10);
    }

    m_tokens.push_back(token);
    return pos - start;
  }

  size_t ParseRegister(size_t start) {
    size_t pos = start;
    std::string regStr;

    // Parse register name (letters and digits)
    while (pos < m_expression.length() && (std::isalnum(m_expression[pos]))) {
      regStr += std::tolower(m_expression[pos++]);
    }

    Token token;
    token.type = TokenType::REGISTER;
    token.value = regStr;
    token.reg = ParseRegisterName(regStr);

    m_tokens.push_back(token);
    return pos - start;
  }

  x86_64::Register ParseRegisterName(const std::string &name) {
    if (name == "rax")
      return x86_64::Register::RAX;
    if (name == "rbx")
      return x86_64::Register::RBX;
    if (name == "rcx")
      return x86_64::Register::RCX;
    if (name == "rdx")
      return x86_64::Register::RDX;
    if (name == "rsi")
      return x86_64::Register::RSI;
    if (name == "rdi")
      return x86_64::Register::RDI;
    if (name == "rbp")
      return x86_64::Register::RBP;
    if (name == "rsp")
      return x86_64::Register::RSP;
    if (name == "r8")
      return x86_64::Register::R8;
    if (name == "r9")
      return x86_64::Register::R9;
    if (name == "r10")
      return x86_64::Register::R10;
    if (name == "r11")
      return x86_64::Register::R11;
    if (name == "r12")
      return x86_64::Register::R12;
    if (name == "r13")
      return x86_64::Register::R13;
    if (name == "r14")
      return x86_64::Register::R14;
    if (name == "r15")
      return x86_64::Register::R15;
    if (name == "rip")
      return x86_64::Register::RIP;
    if (name == "eax")
      return x86_64::Register::RAX;
    if (name == "ebx")
      return x86_64::Register::RBX;
    if (name == "ecx")
      return x86_64::Register::RCX;
    if (name == "edx")
      return x86_64::Register::RDX;
    return x86_64::Register::NONE;
  }

  bool ParseLogicalOr() {
    bool left = ParseLogicalAnd();
    while (CurrentToken().type == TokenType::LOGICAL &&
           CurrentToken().value == "||") {
      ConsumeToken();
      bool right = ParseLogicalAnd();
      left = left || right;
    }
    return left;
  }

  bool ParseLogicalAnd() {
    bool left = ParseComparison();
    while (CurrentToken().type == TokenType::LOGICAL &&
           CurrentToken().value == "&&") {
      ConsumeToken();
      bool right = ParseComparison();
      left = left && right;
    }
    return left;
  }

  bool ParseComparison() {
    uint64_t left = ParseArithmetic();
    if (CurrentToken().type == TokenType::OPERATOR) {
      std::string op = CurrentToken().value;
      if (op == "==" || op == "!=" || op == ">" || op == "<" || op == ">=" ||
          op == "<=") {
        ConsumeToken();
        uint64_t right = ParseArithmetic();
        if (op == "==")
          return left == right;
        if (op == "!=")
          return left != right;
        if (op == ">")
          return left > right;
        if (op == "<")
          return left < right;
        if (op == ">=")
          return left >= right;
        if (op == "<=")
          return left <= right;
      }
    }
    return left != 0;
  }

  uint64_t ParseArithmetic() {
    uint64_t left = ParseTerm();
    while (CurrentToken().type == TokenType::OPERATOR &&
           (CurrentToken().value == "+" || CurrentToken().value == "-")) {
      std::string op = CurrentToken().value;
      ConsumeToken();
      uint64_t right = ParseTerm();
      if (op == "+")
        left += right;
      else if (op == "-")
        left -= right;
    }
    return left;
  }

  uint64_t ParseTerm() {
    uint64_t left = ParseFactor();
    while (CurrentToken().type == TokenType::OPERATOR &&
           (CurrentToken().value == "*" || CurrentToken().value == "/" ||
            CurrentToken().value == "%")) {
      std::string op = CurrentToken().value;
      ConsumeToken();
      uint64_t right = ParseFactor();
      if (op == "*")
        left *= right;
      else if (op == "/" && right != 0)
        left /= right;
      else if (op == "%" && right != 0)
        left %= right;
    }
    return left;
  }

  uint64_t ParseFactor() {
    Token token = CurrentToken();

    if (token.type == TokenType::NUMBER) {
      ConsumeToken();
      return token.numValue;
    }

    if (token.type == TokenType::REGISTER) {
      ConsumeToken();
      if (token.reg != x86_64::Register::NONE) {
        return m_cpu.GetRegister(token.reg);
      }
      return 0;
    }

    if (token.type == TokenType::LBRACKET) {
      ConsumeToken();
      uint64_t address = ParseArithmetic();
      if (CurrentToken().type == TokenType::RBRACKET) {
        ConsumeToken();
        try {
          uint64_t value = 0;
          m_mmu.ReadVirtual(address, &value, sizeof(uint64_t), 1);
          return value;
        } catch (...) {
          return 0;
        }
      }
      return 0;
    }

    if (token.type == TokenType::LPAREN) {
      ConsumeToken();
      uint64_t result = ParseArithmetic();
      if (CurrentToken().type == TokenType::RPAREN) {
        ConsumeToken();
      }
      return result;
    }

    return 0;
  }

  const Token &CurrentToken() const {
    if (m_currentToken < m_tokens.size()) {
      return m_tokens[m_currentToken];
    }
    static Token endToken{TokenType::END, ""};
    return endToken;
  }

  void ConsumeToken() {
    if (m_currentToken < m_tokens.size()) {
      m_currentToken++;
    }
  }
};

/**
 * @brief Sets the entry point for all CPU cores.
 * @param entryPoint The entry point address for execution.
 */
void PS4Emulator::SetCPUEntryPoints(uint64_t entryPoint) {
  EMULATOR_LOCK(m_emulatorMutex);

  spdlog::info("Setting CPU entry points to 0x{:x} for {} cores", entryPoint,
               m_cpus.size());

  for (size_t i = 0; i < m_cpus.size(); ++i) {
    if (m_cpus[i]) {
      m_cpus[i]->SetRegister(x86_64::Register::RIP, entryPoint);
      uint64_t stackBase = 0x7FFFF0000000ULL + (i * 0x100000);
      m_cpus[i]->SetRegister(x86_64::Register::RSP, stackBase);
      m_cpus[i]->SetRegister(x86_64::Register::RBP, stackBase);
      spdlog::debug("CPU {} entry point set: RIP=0x{:x}, RSP=0x{:x}", i,
                    entryPoint, stackBase);
    }
  }

  m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
}

/**
 * @brief Evaluates a breakpoint condition expression.
 * @param condition The condition string to evaluate.
 * @param cpu The CPU instance for register access.
 * @return True if condition is met, false otherwise.
 */
bool PS4Emulator::EvaluateBreakpointCondition(const std::string &condition,
                                              x86_64::X86_64CPU &cpu) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (condition.empty()) {
      return true;
    }

    BreakpointExpressionParser parser(condition, cpu, *m_mmu);
    bool result = parser.Parse();

    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);

    spdlog::trace("Breakpoint condition '{}' evaluated to: {}", condition,
                  result ? "true" : "false");

    return result;
  } catch (const std::exception &e) {
    spdlog::error("EvaluateBreakpointCondition failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return true;
  }
}

bool PS4Emulator::InitializeCPUs() {
  try {
    spdlog::info("Initializing {} CPU cores with distributed entry points",
                 m_cpus.size());

    for (size_t i = 0; i < m_cpus.size(); ++i) {
      spdlog::info("Initializing CPU core {}", i);

      if (!m_cpus[i]->Initialize()) {
        spdlog::error("Failed to initialize CPU core {}", i);
        return false;
      }

      uint64_t baseEntry = 0x400000;
      uint64_t coreOffset = i * 0x1000;
      uint64_t entryPoint = baseEntry + coreOffset;

      try {
        std::vector<uint8_t> initialProgram = {
            0x90, // NOP
            0x90, // NOP
            0x90, // NOP
            0xF4  // HLT
        };

        m_mmu->WriteVirtual(entryPoint, initialProgram.data(),
                            initialProgram.size(), 0);
        m_cpus[i]->SetRegister(x86_64::Register::RIP, entryPoint);

        spdlog::info("CPU[{}]: Entry point set to 0x{:x} with basic program", i,
                     entryPoint);

        if (i < m_cpus.size() - 1) {
          std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }

      } catch (const std::exception &e) {
        spdlog::error("Failed to setup entry point for CPU[{}]: {}", i,
                      e.what());
        return false;
      }
    }

    spdlog::info(
        "All CPU cores initialized successfully with distributed entry points");
    return true;

  } catch (const std::exception &e) {
    spdlog::error("CPU initialization failed: {}", e.what());
    return false;
  }
}

// --- ADD: Resource monitoring thread ---
void PS4Emulator::ResourceMonitorThread() {
  while (m_running) {
    try {
      // Monitor memory usage
      // auto memInfo = GetMemoryInfo(); // This function would need to be
      // implemented if (memInfo.availableMemory < 512 * 1024 * 1024) { // Less
      // than 512MB available
      //   spdlog::warn("Low memory warning: {} MB available",
      //                memInfo.availableMemory / (1024 * 1024));
      //   m_enhancedStats->memoryPressureEvents++;

      //   // Trigger memory cleanup
      //   // TriggerMemoryCleanup(); // This function would need to be
      //   implemented
      // }

      // Monitor CPU temperature (platform-specific)
      // auto cpuTemp = GetCPUTemperature(); // This function would need to be
      // implemented if (cpuTemp > 85.0) { // 85°C threshold
      //   spdlog::warn("High CPU temperature: {:.1f}°C", cpuTemp);
      //   // Potentially throttle emulation speed
      //   // AdjustEmulationSpeed(0.8); // This function would need to be
      //   implemented
      // }

      // Monitor disk I/O
      // auto diskStats = GetDiskIOStats(); // This function would need to be
      // implemented if (diskStats.queueDepth > 32) {
      //   spdlog::warn("High disk I/O queue depth: {}", diskStats.queueDepth);
      // }

      std::this_thread::sleep_for(std::chrono::seconds(1));

    } catch (const std::exception &e) {
      spdlog::error("Resource monitor error: {}", e.what());
    }
  }
}

void PS4Emulator::HandleTSCWrapAround() {
  try {
    spdlog::info("Handling TSC wraparound - resetting timing subsystems");

    // Recalibrate TSC instead of reset (since Reset doesn't exist)
    if (m_tsc) {
      m_tsc->Calibrate();
      spdlog::debug("TSC recalibrated after wraparound");
    }

    // Reset pipeline statistics that depend on cycle counts
    for (auto &cpu : m_cpus) {
      if (cpu) {
        // Get pipeline reference and reset stats
        auto &pipeline = cpu->GetPipeline();
        pipeline.ResetStats();
        spdlog::debug("Reset pipeline stats for CPU core");
      }
    }

    // Log the wraparound event for monitoring
    spdlog::warn("TSC wraparound detected and handled - timing may be "
                 "temporarily affected");

    spdlog::info("TSC wraparound handling completed successfully");

  } catch (const std::exception &e) {
    spdlog::error("Failed to handle TSC wraparound: {}", e.what());
    // Continue execution even if wraparound handling fails
    // This is not a critical error that should stop the emulator
  }
}

} // namespace ps4
