#include "interrupt_handler.h"
#include "../common/lock_ordering.h"
#include "../cpu/x86_64_cpu.h"
#include "../ps4/fiber_manager.h"
#include "../ps4/ps4_emulator.h"
#include "../include/magic_enum/magic_enum_format.hpp"
#include <array>
#include <atomic>
#include <chrono>
#include <fmt/format.h>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <string>
#include <thread>
#include <utility>
#include <vector>

// FIX 1: Replaced recursive_mutex with std::mutex and added separate mutex for
// fiber actions FIX 2: Preallocation of IDT and stubs is handled by passing
// addresses to constructor FIX 3: Fiber actions are deferred to a lock-free
// vector FIX 4: Implemented proper x86_64 handler stubs FIX 7: Reduced logging
// verbosity, using async logging (assumed configured elsewhere) FIX 9: Using
// exponential moving average for latency FIX 10: Added vector bounds checking

namespace x86_64 {
struct InterruptException : std::runtime_error {
  explicit InterruptException(const std::string &msg)
      : std::runtime_error(msg) {}
};

// Helper function to validate vector numbers
inline bool IsValidVector(uint8_t vector, size_t maxVectors) {
  return vector < maxVectors;
}

/**
 * @brief Constructs an InterruptHandler instance.
 * @details Initializes the handler with references to the CPU and memory,
 *          setting up default handlers for all vectors.
 * @param cpu Reference to the X86_64CPU instance.
 * @param memory Reference to the PS4MMU-based memory system.
 */
InterruptHandler::InterruptHandler(X86_64CPU &cpu, ps4::PS4MMU &memory)
    : m_cpu(cpu), m_memory(memory), m_idtBaseAddress(0), m_handlerBase(0),
      m_customHandlers(DEFAULT_IDT_ENTRY_COUNT), m_stats{} {
  for (auto &handler : m_customHandlers) {
    handler = [this](InterruptContext &ctx) { DefaultExceptionHandler(ctx); };
  }
  spdlog::info("InterruptHandler initialized for CPU {}", cpu.GetCPUId());
}

/**
 * @brief Constructs an InterruptHandler instance with preallocated memory.
 * @details Initializes with preallocated IDT and handler stub memory to avoid
 * allocation deadlocks.
 * @param cpu Reference to the X86_64CPU instance.
 * @param memory Reference to the PS4MMU-based memory system.
 * @param idtBase Preallocated IDT base address.
 * @param handlerBase Preallocated handler stubs base address.
 */
InterruptHandler::InterruptHandler(X86_64CPU &cpu, ps4::PS4MMU &memory,
                                   uint64_t idtBase, uint64_t handlerBase)
    : m_cpu(cpu), m_memory(memory), m_idtBaseAddress(idtBase),
      m_handlerBase(handlerBase), m_customHandlers(DEFAULT_IDT_ENTRY_COUNT),
      m_stats{} {
  for (auto &handler : m_customHandlers) {
    handler = [this](InterruptContext &ctx) { DefaultExceptionHandler(ctx); };
  }
  spdlog::info(
      "InterruptHandler initialized for CPU {} with preallocated memory",
      cpu.GetCPUId());
}

/**
 * @brief Destructs the InterruptHandler, ensuring cleanup.
 */
InterruptHandler::~InterruptHandler() noexcept = default;

/**
 * @brief Initializes the InterruptHandler.
 * @details Allocates IDT memory, sets up descriptors, and registers handlers,
 *          ensuring thread safety and fiber integration.
 */
void InterruptHandler::Initialize() {
  spdlog::info("Initializing InterruptHandler...");

  try {
    // Allocate IDT memory if not preallocated
    if (m_idtBaseAddress == 0) {
      spdlog::info("Allocating IDT memory: size=0x{:x}", m_idtSize);
      m_idtBaseAddress = m_memory.AllocateVirtual(
          0, m_idtSize, 16, ps4::PROT_READ | ps4::PROT_WRITE, true);
      spdlog::info("IDT allocated at 0x{:x}", m_idtBaseAddress);
      if (!m_idtBaseAddress) {
        throw InterruptException("Failed to allocate IDT memory");
      }
    }

    spdlog::info("Zeroing IDT memory");
    std::vector<uint8_t> zeroData(m_idtSize, 0);
    m_memory.WriteVirtual(m_idtBaseAddress, zeroData.data(), m_idtSize, 0);
    spdlog::info("IDT memory zeroed");

    // Allocate handler stubs if not preallocated
    if (m_handlerBase == 0) {
      spdlog::info("Allocating handler stubs: entry_count={}, size=0x{:x}",
                   m_idtEntryCount, m_idtEntryCount * 16);
      m_handlerBase = m_memory.AllocateVirtual(
          0, m_idtEntryCount * 16, 16,
          ps4::PROT_READ | ps4::PROT_WRITE | ps4::PROT_EXEC, true);
      spdlog::info("Handler stubs allocated at 0x{:x}", m_handlerBase);
      if (!m_handlerBase) {
        throw InterruptException("Failed to allocate handler stubs");
      }
    }

    spdlog::info("Setting up IDT descriptors");
    uint16_t kernelCodeSegment = 0x08; // Assume GDT kernel CS
    // Ensure we don't exceed uint8_t range for vector numbers
    size_t maxEntries = std::min(m_idtEntryCount, static_cast<size_t>(256));
    for (size_t i = 0; i < maxEntries; ++i) {
      uint64_t stubAddress = m_handlerBase + i * 16;
      if (stubAddress + 1 > m_handlerBase + m_idtEntryCount * 16) {
        throw InterruptException(fmt::format(
            "Stub address 0x{:x} exceeds allocated range 0x{:x}-0x{:x}",
            stubAddress, m_handlerBase, m_handlerBase + m_idtEntryCount * 16));
      }
      // FIX: Use NOP instead of INT3 to prevent immediate interrupts during
      // initialization
      uint8_t stub[] = {0x90}; // NOP instruction instead of INT3
      spdlog::trace("Writing stub for vector {} at 0x{:x}", i, stubAddress);

      // Write stub without holding lock to prevent deadlock
      try {
        m_memory.WriteVirtual(stubAddress, stub, sizeof(stub), 0);
      } catch (const std::exception &e) {
        spdlog::error("Failed to write stub for vector {} at 0x{:x}: {}", i,
                      stubAddress, e.what());
        throw InterruptException(
            fmt::format("Write access denied at 0x{:x}", stubAddress));
      }

      // Set descriptor without holding lock to prevent deadlock
      try {
        SetDescriptorUnsafe(static_cast<uint8_t>(i), stubAddress, kernelCodeSegment,
                            IDT_GATE_INTERRUPT);
      } catch (const std::exception &e) {
        spdlog::error("Failed to set descriptor for vector {}: {}", i,
                      e.what());
        throw;
      }
      if (i % 50 == 0) {
        spdlog::info("Processed descriptor {}", i);
      }
    }
    spdlog::info("IDT descriptors set");

    spdlog::info("Setting up IDT descriptor");
    m_idtDescriptor.limit = static_cast<uint16_t>(m_idtSize - 1);
    m_idtDescriptor.base = m_idtBaseAddress;
    spdlog::info("IDT descriptor set: base=0x{:x}, limit=0x{:x}",
                 m_idtDescriptor.base, m_idtDescriptor.limit);

    // FIX 6: Register handlers for all exception vectors
    // CRITICAL: Bounds check for vector numbers before registration
    if (EXC_DE >= m_customHandlers.size()) {
      spdlog::error("Vector EXC_DE {} out of bounds", EXC_DE);
      throw InterruptException("Invalid exception vector");
    }
    RegisterHandler(EXC_DE, [this](InterruptContext &ctx) {
      spdlog::error("Divide error at RIP=0x{:x}", ctx.rip);
      m_cpu.TriggerInterrupt(EXC_GP, 0, false);
    });
    if (EXC_DB >= m_customHandlers.size()) {
      spdlog::error("Vector EXC_DB {} out of bounds", EXC_DB);
      throw InterruptException("Invalid exception vector");
    }
    RegisterHandler(EXC_DB, [this](InterruptContext &ctx) {
      spdlog::error("Debug exception at RIP=0x{:x}", ctx.rip);
      m_cpu.SetFlag(FLAG_TF, false); // Clear trap flag
    });
    if (EXC_NMI >= m_customHandlers.size()) {
      spdlog::error("Vector EXC_NMI {} out of bounds", EXC_NMI);
      throw InterruptException("Invalid exception vector");
    }
    RegisterHandler(EXC_NMI, [this](InterruptContext &ctx) {
      spdlog::warn("Non-maskable interrupt at RIP=0x{:x}", ctx.rip);
    });
    if (EXC_BP >= m_customHandlers.size()) {
      spdlog::error("Vector EXC_BP {} out of bounds", EXC_BP);
      throw InterruptException("Invalid exception vector");
    }
    RegisterHandler(EXC_BP, [this](InterruptContext &ctx) {
      spdlog::error("Breakpoint at RIP=0x{:x}", ctx.rip);
      m_cpu.SetFlag(FLAG_TF, false);
    });
    if (EXC_OF >= m_customHandlers.size()) {
      spdlog::error("Vector EXC_OF {} out of bounds", EXC_OF);
      throw InterruptException("Invalid exception vector");
    }
    RegisterHandler(EXC_OF, [this](InterruptContext &ctx) {
      spdlog::error("Overflow at RIP=0x{:x}", ctx.rip);
      m_cpu.TriggerInterrupt(EXC_GP, 0, false);
    });
    RegisterHandler(EXC_BR, [this](InterruptContext &ctx) {
      spdlog::error("Bound range exceeded at RIP=0x{:x}", ctx.rip);
      m_cpu.TriggerInterrupt(EXC_GP, 0, false);
    });
    RegisterHandler(EXC_UD, [this](InterruptContext &ctx) {
      spdlog::error("Undefined opcode at RIP=0x{:x}", ctx.rip);
      m_cpu.TriggerInterrupt(EXC_GP, 0, false);
    });
    RegisterHandler(EXC_NM, [this](InterruptContext &ctx) {
      spdlog::error("No math coprocessor at RIP=0x{:x}", ctx.rip);
      // Clear FPU status by setting appropriate flags
      m_cpu.SetFlag(FLAG_CF, false);
    });
    RegisterHandler(EXC_DF,
                    [this](InterruptContext &ctx) { DoubleFaultHandler(ctx); });
    RegisterHandler(EXC_TS, [this](InterruptContext &ctx) {
      spdlog::error("Invalid TSS at RIP=0x{:x}, error=0x{:x}", ctx.rip,
                    ctx.error_code);
      m_cpu.TriggerInterrupt(EXC_GP, 0, false);
    });
    RegisterHandler(EXC_NP, [this](InterruptContext &ctx) {
      spdlog::error("Segment not present at RIP=0x{:x}, error=0x{:x}", ctx.rip,
                    ctx.error_code);
      m_cpu.TriggerInterrupt(EXC_GP, 0, false);
    });
    RegisterHandler(EXC_SS, [this](InterruptContext &ctx) {
      spdlog::error("Stack-segment fault at RIP=0x{:x}, error=0x{:x}", ctx.rip,
                    ctx.error_code);
      m_cpu.TriggerInterrupt(EXC_GP, 0, false);
    });
    RegisterHandler(EXC_GP, [this](InterruptContext &ctx) {
      spdlog::error("General protection fault at RIP=0x{:x}, error=0x{:x}",
                    ctx.rip, ctx.error_code);
      m_cpu.Shutdown();
    });
    RegisterHandler(EXC_PF,
                    [this](InterruptContext &ctx) { PageFaultHandler(ctx); });
    RegisterHandler(EXC_MF, [this](InterruptContext &ctx) {
      spdlog::error("FPU error at RIP=0x{:x}", ctx.rip);
      // Clear FPU status by setting appropriate flags
      m_cpu.SetFlag(FLAG_CF, false);
    });
    RegisterHandler(EXC_AC, [this](InterruptContext &ctx) {
      spdlog::error("Alignment check at RIP=0x{:x}, error=0x{:x}", ctx.rip,
                    ctx.error_code);
      m_cpu.TriggerInterrupt(EXC_GP, 0, false);
    });
    RegisterHandler(EXC_MC, [this](InterruptContext &ctx) {
      spdlog::error("Machine check at RIP=0x{:x}", ctx.rip);
      m_cpu.Shutdown();
    });
    RegisterHandler(EXC_XM, [this](InterruptContext &ctx) {
      spdlog::error("SIMD floating-point exception at RIP=0x{:x}", ctx.rip);
      // Clear SIMD status by setting appropriate flags
      m_cpu.SetFlag(FLAG_CF, false);
    });

    spdlog::info("Custom handlers registered");

    // Load IDT without holding lock to prevent deadlock
    spdlog::info("Loading IDT");
    try {
      if (!LoadIDT()) {
        throw InterruptException("Failed to load IDT");
      }
      spdlog::info("IDT loaded successfully");
    } catch (const std::exception &e) {
      spdlog::error("IDT loading failed: {}", e.what());
      throw InterruptException("IDT loading failed: " + std::string(e.what()));
    }

    spdlog::info("InterruptHandler initialization completed");
  } catch (const InterruptException &e) {
    spdlog::critical("InterruptHandler initialization failed: {}", e.what());
    throw;
  }
}

/**
 * @brief Loads the IDT into the CPU.
 * @return True on success, false on failure.
 */
bool InterruptHandler::LoadIDT() {
  INTERRUPT_LOCK(m_mutex);
  spdlog::info("Attempting to load IDT...");
  try {
#ifdef __GNUC__
    asm volatile("lidt %0" : : "m"(m_idtDescriptor));
#else
    spdlog::warn("LoadIDT not implemented for MSVC; simulating IDT load");
    m_cpu.SetIDTR(m_idtDescriptor.base, m_idtDescriptor.limit);
#endif
    spdlog::info("LoadIDT completed");
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Failed to load IDT: {}", e.what());
    return false;
  }
}

/**
 * @brief Internal helper to set an IDT descriptor.
 * @param vector The interrupt vector.
 * @param handlerAddress The handler’s address.
 * @param selector The segment selector.
 * @param type_attributes The gate type and attributes.
 * @param ist The Interrupt Stack Table index.
 */
void InterruptHandler::SetDescriptorInternal(uint8_t vector,
                                             uint64_t handlerAddress,
                                             uint16_t selector,
                                             uint8_t type_attributes,
                                             uint8_t ist) {
  InterruptDescriptor desc = {};
  desc.offset_1 = handlerAddress & 0xFFFF;
  desc.selector = selector;
  desc.ist = ist & 0x7;
  desc.type_attributes = type_attributes | 0x80; // Present bit
  desc.offset_2 = (handlerAddress >> 16) & 0xFFFF;
  desc.offset_3 = (handlerAddress >> 32) & 0xFFFFFFFF;
  desc.reserved = 0;
  uint64_t descriptorAddress =
      m_idtBaseAddress + (vector * sizeof(InterruptDescriptor));
  if (descriptorAddress + sizeof(InterruptDescriptor) >
      m_idtBaseAddress + m_idtSize) {
    throw InterruptException(fmt::format(
        "Descriptor address 0x{:x} exceeds IDT range 0x{:x}-0x{:x}",
        descriptorAddress, m_idtBaseAddress, m_idtBaseAddress + m_idtSize));
  }

  try {
#ifdef DEBUG
    spdlog::debug("Writing IDT descriptor for vector {} at 0x{:x}", vector,
                  descriptorAddress);
#endif
    m_memory.WriteVirtual(descriptorAddress, &desc, sizeof(desc), 0);
  } catch (const std::exception &e) {
    throw InterruptException(
        fmt::format("Write access denied at 0x{:x}", descriptorAddress));
  }
}

/**
 * @brief Sets an IDT descriptor for a vector.
 * @param vector The interrupt vector.
 * @param handlerAddress The handler's address.
 * @param selector The segment selector.
 * @param type_attributes The gate type and attributes.
 * @param ist The Interrupt Stack Table index.
 */
void InterruptHandler::SetDescriptor(uint8_t vector, uint64_t handlerAddress,
                                     uint16_t selector, uint8_t type_attributes,
                                     uint8_t ist) {
  INTERRUPT_LOCK(m_mutex);
  SetDescriptorInternal(vector, handlerAddress, selector, type_attributes, ist);
}

/**
 * @brief Sets an IDT descriptor for a vector without acquiring locks.
 * @details This method is intended for use during initialization when
 *          thread safety is not required. It avoids lock ordering violations
 *          by not acquiring the InterruptMutex before calling memory operations.
 * @param vector The interrupt vector.
 * @param handlerAddress The handler's address.
 * @param selector The segment selector.
 * @param type_attributes The gate type and attributes.
 * @param ist The Interrupt Stack Table index.
 * @warning Only use during single-threaded initialization!
 */
void InterruptHandler::SetDescriptorUnsafe(uint8_t vector, uint64_t handlerAddress,
                                           uint16_t selector, uint8_t type_attributes,
                                           uint8_t ist) {
  // Call SetDescriptorInternal directly without acquiring InterruptMutex
  // to prevent lock ordering violations during initialization
  SetDescriptorInternal(vector, handlerAddress, selector, type_attributes, ist);
}

/**
 * @brief Retrieves an IDT descriptor for a vector.
 * @param vector The interrupt vector.
 * @param desc The output descriptor.
 * @return True if the descriptor is valid, false otherwise.
 */
bool InterruptHandler::GetDescriptor(uint8_t vector,
                                     InterruptDescriptor &desc) {
  INTERRUPT_LOCK(m_mutex);
  uint64_t descriptorAddress =
      m_idtBaseAddress + (vector * sizeof(InterruptDescriptor));
  try {
    m_memory.ReadVirtual(descriptorAddress, &desc, sizeof(desc), 0);
    return (desc.type_attributes & 0x80) != 0; // Present bit
  } catch (const std::exception &e) {
    spdlog::error("Read IDT vector {} failed: {}", vector, e.what());
    return false;
  }
}

/**
 * @brief Registers a custom handler for an interrupt vector.
 * @param vector The interrupt vector.
 * @param handler The handler function.
 */
void InterruptHandler::RegisterHandler(uint8_t vector, HandlerFunc handler) {
  INTERRUPT_LOCK(m_mutex);
  if (vector >= m_customHandlers.size()) {
    m_customHandlers.resize(vector + 1, [this](InterruptContext &ctx) {
      DefaultExceptionHandler(ctx);
    });
  }
  m_customHandlers[vector] = std::move(handler);
}

/**
 * @brief Handles an interrupt or exception.
 * @param vector The interrupt vector.
 * @param errorCode The error code (if any).
 * @param isSoftwareInterrupt True if it's a software interrupt.
 */
void InterruptHandler::HandleInterrupt(uint8_t vector, uint64_t errorCode,
                                       bool isSoftwareInterrupt) {
  // CRITICAL DEADLOCK PREVENTION: Check if we're already in interrupt handling
  thread_local bool in_interrupt_handler = false;
  if (in_interrupt_handler) {
    spdlog::warn("Recursive interrupt handling detected, deferring vector {}",
                 vector);
    // Store for deferred processing to avoid recursion deadlock
    {
      INTERRUPT_LOCK(m_mutex);
      m_deferredInterrupts.push({vector, errorCode, isSoftwareInterrupt, 255});
    }
    return;
  }

  // FIX 10: Validate vector range
  if (vector >= m_idtEntryCount) {
    spdlog::error("Invalid interrupt vector: {}", vector);
    // CRITICAL DEADLOCK FIX: Don't call CPU methods that might deadlock
    // Just log and return instead of triggering another interrupt
    return;
  }

  in_interrupt_handler = true;

  try {
    uint8_t priority = (vector <= 31) ? 255 : (vector < 64 ? 128 : 64);
    {
      INTERRUPT_LOCK(m_mutex);
      m_pendingInterrupts.push(
          {vector, errorCode, isSoftwareInterrupt, priority});
    }

    // Process the highest-priority interrupt
    PendingInterrupt interrupt;
    {
      INTERRUPT_LOCK(m_mutex);
      if (!m_pendingInterrupts.empty()) {
        interrupt = m_pendingInterrupts.top();
        m_pendingInterrupts.pop();
      } else {
        in_interrupt_handler = false;
        return; // No interrupts to process
      }
    }

    ProcessInterrupt(interrupt.vector, interrupt.errorCode,
                     interrupt.isSoftwareInterrupt);

    // Process any deferred interrupts
    while (true) {
      PendingInterrupt deferred;
      {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (m_deferredInterrupts.empty()) {
          break;
        }
        deferred = m_deferredInterrupts.front();
        m_deferredInterrupts.pop();
      }
      spdlog::debug("Processing deferred interrupt vector {}", deferred.vector);
      ProcessInterrupt(deferred.vector, deferred.errorCode,
                       deferred.isSoftwareInterrupt);
    }
  } catch (const std::exception &e) {
    spdlog::error("Exception in interrupt handler: {}", e.what());
  }

  in_interrupt_handler = false;
}

/**
 * @brief Processes a single interrupt from the queue.
 * @param vector The interrupt vector.
 * @param errorCode The error code (if any).
 * @param isSoftwareInterrupt True if it's a software interrupt.
 */
void InterruptHandler::ProcessInterrupt(uint8_t vector, uint64_t errorCode,
                                        bool isSoftwareInterrupt) {
  auto start = std::chrono::steady_clock::now();

  // Gather CPU state
  InterruptContext context;
  context.vector = vector;
  context.error_code = errorCode;
  context.rip = m_cpu.GetRegister(Register::RIP);
  context.cs = m_cpu.GetCS();
  context.rflags = m_cpu.GetRflags();
  context.rsp = m_cpu.GetRegister(Register::RSP);
  context.ss = m_cpu.GetSS();

  bool isHardwareInterrupt = vector >= 32;
  bool isNMI = vector == EXC_NMI;
  if (isHardwareInterrupt && !isNMI && !m_cpu.GetFlag(FLAG_IF)) {
#ifdef DEBUG
    spdlog::debug("Ignoring IRQ {} due to IF=0", vector);
#endif
    return;
  }

  InterruptDescriptor desc;
  uint8_t currentCPL;
  uint64_t stackToUse = context.rsp;
  uint16_t ssToUse = context.ss;

  {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_stats[vector]++;
    if (!GetDescriptor(vector, desc)) {
      spdlog::error("No valid IDT descriptor for vector {}", vector);
      DefaultExceptionHandler(context);
      return;
    }
    currentCPL = context.cs & 0x3;
  }

  if ((desc.type_attributes & 0x80) == 0) {
    DefaultExceptionHandler(context);
    return;
  }

  uint8_t descriptorDPL = (desc.type_attributes >> 5) & 3;
  if (isSoftwareInterrupt && currentCPL > descriptorDPL) {
    spdlog::warn("Privilege violation for INT {}", vector);
    m_cpu.TriggerInterrupt(EXC_GP, vector * 8 + 2, false);
    return;
  }

  if (desc.ist != 0) {
    stackToUse = m_cpu.GetTSSBase() + desc.ist * 0x1000;
    ssToUse = m_cpu.GetKernelSS();
  }

  try {
    uint64_t currentRSP = stackToUse;
    bool pushesErrorCode =
        (vector == EXC_DF || (vector >= EXC_TS && vector <= EXC_PF) ||
         vector == EXC_AC);

    currentRSP -= 8;
    m_memory.WriteVirtual(currentRSP, &ssToUse, 8, 0);
    currentRSP -= 8;
    m_memory.WriteVirtual(currentRSP, &context.rsp, 8, 0);
    currentRSP -= 8;
    m_memory.WriteVirtual(currentRSP, &context.rflags, 8, 0);
    currentRSP -= 8;
    m_memory.WriteVirtual(currentRSP, &context.cs, 8, 0);
    currentRSP -= 8;
    m_memory.WriteVirtual(currentRSP, &context.rip, 8, 0);
    if (pushesErrorCode) {
      currentRSP -= 8;
      m_memory.WriteVirtual(currentRSP, &context.error_code, 8, 0);
    }
    m_cpu.SetRegister(Register::RSP, currentRSP);
  } catch (const std::exception &e) {
    spdlog::critical("Stack push failed for IRQ {}: {}", vector, e.what());
    m_cpu.TriggerInterrupt(EXC_DF, 0, false);
    return;
  }

  uint64_t newRflags = context.rflags;
  if ((desc.type_attributes & 0xF) == 0xE) {
    newRflags &= ~FLAG_IF;
  }
  newRflags &= ~(FLAG_TF | FLAG_AC);
  m_cpu.SetRflags(newRflags);

  uint64_t handlerAddress = ((uint64_t)desc.offset_3 << 32) |
                            ((uint64_t)desc.offset_2 << 16) | desc.offset_1;
  m_cpu.SetRegister(Register::RIP, handlerAddress);

  context.rsp = m_cpu.GetRegister(Register::RSP);
  context.rflags = m_cpu.GetRflags();

  // FIX 3: Defer fiber management with safety checks
  try {
    ps4::FiberManager &fiberManager =
        ps4::PS4Emulator::GetInstance().GetFiberManager();
    uint64_t currentFiberId = fiberManager.GetCurrentFiberId();
    if (currentFiberId != 0) {
      std::lock_guard<std::mutex> lock(m_fiberMutex);
      m_pendingFiberActions.push_back({currentFiberId, true});
    }
  } catch (const std::exception &e) {
    spdlog::error(
        "Failed to access FiberManager during interrupt processing: {}",
        e.what());
    // Continue without fiber management to prevent crash
  }

  try {
    std::function<void(InterruptContext &)> handler;
    {
      std::lock_guard<std::mutex> lock(m_mutex);
      handler = m_customHandlers[vector];
    }
    handler(context);

    if (vector <= 31) {
      m_cpu.SetRegister(Register::RSP, context.rsp);
      m_cpu.SetRflags(context.rflags);
      m_cpu.SetRegister(Register::RIP, context.rip);
    }
  } catch (const std::exception &e) {
    spdlog::error("Exception in handler for vector {}: {}", vector, e.what());
    m_cpu.TriggerInterrupt(EXC_DF, 0, false);
  }

  // Process deferred fiber actions
  std::vector<FiberAction> fiberActions;
  {
    std::lock_guard<std::mutex> lock(m_fiberMutex);
    fiberActions = std::move(m_pendingFiberActions);
    m_pendingFiberActions.clear();
  }

  // Process fiber actions with safety checks
  if (!fiberActions.empty()) {
    try {
      ps4::FiberManager &fiberManager =
          ps4::PS4Emulator::GetInstance().GetFiberManager();
      for (const auto &action : fiberActions) {
        if (action.suspend) {
          fiberManager.SuspendFiber();
        } else {
          fiberManager.ResumeFiber(action.fiberId);
        }
      }
    } catch (const std::exception &e) {
      spdlog::error("Failed to process deferred fiber actions: {}", e.what());
      // Continue without processing fiber actions to prevent crash
    }
  }

  // FIX 9: Update latency with exponential moving average
  auto end = std::chrono::steady_clock::now();
  uint64_t latency =
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  {
    std::lock_guard<std::mutex> lock(m_mutex);
    constexpr double alpha = 0.1;
    m_interruptLatencyUs = static_cast<uint64_t>(
        alpha * latency + (1.0 - alpha) * m_interruptLatencyUs);
  }
}

/**
 * @brief Default handler for unhandled exceptions.
 * @param context The interrupt context.
 */
void InterruptHandler::DefaultExceptionHandler(InterruptContext &context) {
  spdlog::error("Unhandled exception: vector=0x{:x}, RIP=0x{:x}, error=0x{:x}",
                context.vector, context.rip, context.error_code);
  m_cpu.Shutdown();
}

/**
 * @brief Handler for double fault exceptions.
 * @param context The interrupt context.
 */
void InterruptHandler::DoubleFaultHandler(InterruptContext &context) {
  spdlog::error("Double fault: vector=0x{:x}, RIP=0x{:x}, error=0x{:x}",
                context.vector, context.rip, context.error_code);
  auto diagnostics = m_cpu.GetDiagnostics();
  for (const auto &[key, value] : diagnostics) {
    spdlog::error("Diagnostic: {} = {}", key, value);
  }
  m_cpu.Shutdown();
}

/**
 * @brief Handler for spurious interrupts.
 * @param context The interrupt context.
 */
void InterruptHandler::SpuriousInterruptHandler(InterruptContext &context) {
  spdlog::warn("Spurious interrupt: vector=0x{:x}", context.vector);
}

/**
 * @brief Handler for page fault exceptions.
 * @param context The interrupt context.
 */
void InterruptHandler::PageFaultHandler(InterruptContext &context) {
  // FIX 5: Enhanced page fault handling
  uint64_t faultAddr = m_cpu.GetCR2();
  spdlog::error("Page fault at RIP=0x{:x}, address=0x{:x}, error=0x{:x}",
                context.rip, faultAddr, context.error_code);
  bool present = (context.error_code & 0x1) == 0;
  bool write = (context.error_code & 0x2) != 0;
  bool user = (context.error_code & 0x4) != 0;
  bool reserved = (context.error_code & 0x8) != 0;
  bool instruction = (context.error_code & 0x10) != 0;

  if (reserved) {
    spdlog::error("Page fault due to reserved bit violation");
    m_cpu.TriggerInterrupt(EXC_GP, 0, false);
    return;
  }

  uint64_t pageAddr = faultAddr & ~0xFFF;
  int prot = write ? (ps4::PROT_READ | ps4::PROT_WRITE) : ps4::PROT_READ;
  if (user || instruction) {
    prot |= ps4::PROT_EXEC;
  }

  try {
    if (present) {
      m_memory.AllocateVirtual(m_cpu.GetProcessId(), 4096, 4096, prot, true);
    } else {
      m_memory.ProtectMemory(pageAddr, 4096, prot, m_cpu.GetProcessId());
    }
    m_cpu.InvalidateTLB(pageAddr);
#ifdef DEBUG
    spdlog::debug("Page fault resolved at 0x{:x}", pageAddr);
#endif
  } catch (const std::exception &e) {
    spdlog::error("Page fault resolution failed: {}", e.what());
    m_cpu.TriggerInterrupt(EXC_GP, 0, false);
  }
}

/**
 * @brief Saves the InterruptHandler state.
 * @param out The output stream.
 */
void InterruptHandler::SaveState(std::ostream &out) const {
  std::lock_guard<std::mutex> lock(m_mutex);
  uint32_t version = 1;
  out.write(reinterpret_cast<const char *>(&version), sizeof(version));
  out.write(reinterpret_cast<const char *>(&m_idtBaseAddress),
            sizeof(m_idtBaseAddress));
  out.write(reinterpret_cast<const char *>(&m_idtEntryCount),
            sizeof(m_idtEntryCount));
  uint32_t statCount = static_cast<uint32_t>(m_stats.size());
  out.write(reinterpret_cast<const char *>(&statCount), sizeof(statCount));
  for (const auto &[vector, count] : m_stats) {
    out.write(reinterpret_cast<const char *>(&vector), sizeof(vector));
    out.write(reinterpret_cast<const char *>(&count), sizeof(count));
  }
  uint32_t pendingCount = static_cast<uint32_t>(m_pendingInterrupts.size());
  out.write(reinterpret_cast<const char *>(&pendingCount),
            sizeof(pendingCount));
  auto tempQueue = m_pendingInterrupts;
  while (!tempQueue.empty()) {
    auto interrupt = tempQueue.top();
    tempQueue.pop();
    out.write(reinterpret_cast<const char *>(&interrupt.vector),
              sizeof(interrupt.vector));
    out.write(reinterpret_cast<const char *>(&interrupt.errorCode),
              sizeof(interrupt.errorCode));
    out.write(reinterpret_cast<const char *>(&interrupt.isSoftwareInterrupt),
              sizeof(interrupt.isSoftwareInterrupt));
    out.write(reinterpret_cast<const char *>(&interrupt.priority),
              sizeof(interrupt.priority));
  }
#ifdef DEBUG
  spdlog::debug("InterruptHandler state saved");
#endif
}

/**
 * @brief Loads the InterruptHandler state.
 * @param in The input stream.
 */
void InterruptHandler::LoadState(std::istream &in) {
  // FIX 8: Transactional state loading
  std::lock_guard<std::mutex> lock(m_mutex);
  uint32_t version;
  in.read(reinterpret_cast<char *>(&version), sizeof(version));
  if (!in.good() || version != 1) {
    throw InterruptException(
        "Invalid InterruptHandler state version or corrupted stream");
  }

  uint64_t idtBaseAddress;
  size_t idtEntryCount;
  std::unordered_map<uint8_t, uint64_t> stats;
  std::vector<PendingInterrupt> pendingInterrupts;

  in.read(reinterpret_cast<char *>(&idtBaseAddress), sizeof(idtBaseAddress));
  in.read(reinterpret_cast<char *>(&idtEntryCount), sizeof(idtEntryCount));
  if (!in.good()) {
    throw InterruptException("Failed to read IDT base or entry count");
  }

  uint32_t statCount;
  in.read(reinterpret_cast<char *>(&statCount), sizeof(statCount));
  for (uint32_t i = 0; i < statCount && in.good(); ++i) {
    uint8_t vector;
    uint64_t count;
    in.read(reinterpret_cast<char *>(&vector), sizeof(vector));
    in.read(reinterpret_cast<char *>(&count), sizeof(count));
    if (in.good()) {
      stats[vector] = count;
    } else {
      throw InterruptException("Failed to read stats");
    }
  }

  uint32_t pendingCount;
  in.read(reinterpret_cast<char *>(&pendingCount), sizeof(pendingCount));
  for (uint32_t i = 0; i < pendingCount && in.good(); ++i) {
    PendingInterrupt interrupt;
    in.read(reinterpret_cast<char *>(&interrupt.vector),
            sizeof(interrupt.vector));
    in.read(reinterpret_cast<char *>(&interrupt.errorCode),
            sizeof(interrupt.errorCode));
    in.read(reinterpret_cast<char *>(&interrupt.isSoftwareInterrupt),
            sizeof(interrupt.isSoftwareInterrupt));
    in.read(reinterpret_cast<char *>(&interrupt.priority),
            sizeof(interrupt.priority));
    if (in.good()) {
      pendingInterrupts.push_back(interrupt);
    } else {
      throw InterruptException("Failed to read pending interrupts");
    }
  }

  m_idtBaseAddress = idtBaseAddress;
  m_idtEntryCount = idtEntryCount;
  m_idtSize = m_idtEntryCount * sizeof(InterruptDescriptor);
  m_customHandlers.resize(m_idtEntryCount, [this](InterruptContext &ctx) {
    DefaultExceptionHandler(ctx);
  });
  m_stats = std::move(stats);
  m_pendingInterrupts = std::priority_queue<PendingInterrupt>();
  for (const auto &interrupt : pendingInterrupts) {
    m_pendingInterrupts.push(interrupt);
  }
#ifdef DEBUG
  spdlog::debug("InterruptHandler state loaded");
#endif
}

/**
 * @brief Gets a custom handler for a vector (for external dispatcher).
 * @param vector The interrupt vector.
 * @return The handler function.
 */
InterruptHandler::HandlerFunc
InterruptHandler::GetHandler(uint8_t vector) const {
  std::lock_guard<std::mutex> lock(m_mutex);
  if (vector < m_customHandlers.size()) {
    return m_customHandlers[vector];
  }
  // Return a default handler that logs the error
  return [](InterruptContext &ctx) {
    spdlog::error(
        "Unhandled exception: vector=0x{:x}, RIP=0x{:x}, error=0x{:x}",
        ctx.vector, ctx.rip, ctx.error_code);
  };
}

/**
 * @brief Dispatcher function for handler stubs.
 * @param vector The interrupt vector.
 * @param context The interrupt context.
 */
extern "C" void InterruptDispatcher(uint8_t vector, InterruptContext *context) {
  InterruptHandler *handler =
      &ps4::PS4Emulator::GetInstance().GetInterruptHandler();
  auto handlerFunc = handler->GetHandler(vector);
  handlerFunc(*context);
}
} // namespace x86_64